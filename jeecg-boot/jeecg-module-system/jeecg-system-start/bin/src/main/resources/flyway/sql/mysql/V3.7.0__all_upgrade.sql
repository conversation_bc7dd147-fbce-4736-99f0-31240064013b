ALTER TABLE `jimu_report_link` 
ADD COLUMN `expression` varchar(255) NULL COMMENT '表达式';

-- 补充online模块接口权限
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1535225446864011265', '1460888189937176577', '批量删除', NULL, NULL, 0, NULL, NULL, 2, 'online:report:deleteBatch', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-10 19:40:44', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1699374612388773890', '1460888189937176577', '获取字典列表', NULL, NULL, 0, NULL, NULL, 2, 'online:report:getDictList', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2023-09-06 18:50:55', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1699374801816125442', '1460888189937176577', '添加Online报表', NULL, NULL, 0, NULL, NULL, 2, 'online:report:add', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2023-09-06 18:51:40', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1699375086147993601', '1460888189937176577', '修改Online报表', NULL, NULL, 0, NULL, NULL, 2, 'online:report:edit', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2023-09-06 18:52:48', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1699375193576701953', '1460888189937176577', '删除Online报表', NULL, NULL, 0, NULL, NULL, 2, 'online:report:delete', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2023-09-06 18:53:14', NULL, NULL, 0, 0, '1', 0);

UPDATE `sys_permission` SET `parent_id` = '1460888189937176577', `name` = 'SQL解析', `url` = NULL, `component` = NULL, `is_route` = 0, `component_name` = NULL, `redirect` = NULL, `menu_type` = 2, `perms` = 'online:report:parseSql', `perms_type` = '1', `sort_no` = NULL, `always_show` = 0, `icon` = NULL, `is_leaf` = 1, `keep_alive` = 0, `hidden` = 0, `hide_tab` = 0, `description` = NULL, `create_by` = 'admin', `create_time` = '2023-09-06 18:51:17', `update_by` = NULL, `update_time` = NULL, `del_flag` = 0, `rule_flag` = 0, `status` = '1', `internal_or_external` = 0 WHERE `id` = '1699374704168534017';

INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1534500282601893890', '1455101470794850305', '代码生成', NULL, NULL, 0, NULL, NULL, 2, 'online:form:generateCode', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-08 19:39:11', 'admin', '2022-06-30 13:39:19', 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1535226870641475586', '1455101470794850305', '批量删除', NULL, NULL, 0, NULL, NULL, 2, 'online:form:deleteBatch', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-10 19:46:23', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1535227149789184001', '1455101470794850305', '新建SQL增强', NULL, NULL, 0, NULL, NULL, 2, 'online:form:enhanceSql:save', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-10 19:47:30', 'admin', '2022-06-30 13:42:36', 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1542383139476467713', '1455101470794850305', '编辑SQL增强', NULL, NULL, 0, NULL, NULL, 2, 'online:form:enhanceSql:edit', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-30 13:42:51', 'admin', '2022-06-30 13:43:38', 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1542383223979110402', '1455101470794850305', '删除SQL增强', NULL, NULL, 0, NULL, NULL, 2, 'online:form:enhanceSql:delete', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-30 13:43:11', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1542383292690198530', '1455101470794850305', '批量删除SQL增强', NULL, NULL, 0, NULL, NULL, 2, 'online:form:enhanceSql:batchDelete', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-30 13:43:28', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1542383437808922625', '1455101470794850305', '导入数据库表', NULL, NULL, 0, NULL, NULL, 2, 'online:form:importTable', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-30 13:44:02', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1542383507883159553', '1455101470794850305', '添加JAVA增强', NULL, NULL, 0, NULL, NULL, 2, 'online:form:enhanceJava:save', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-30 13:44:19', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1542383573423353858', '1455101470794850305', '修改JAVA增强', NULL, NULL, 0, NULL, NULL, 2, 'online:form:enhanceJava:edit', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-30 13:44:35', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1542383826117586945', '1455101470794850305', '删除Online表单', NULL, NULL, 0, NULL, NULL, 2, 'online:form:delete', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-30 13:45:35', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1542383907281563650', '1455101470794850305', '移除Online表单', NULL, NULL, 0, NULL, NULL, 2, 'online:form:remove', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2022-06-30 13:45:54', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1699374346553786370', '1455101470794850305', '添加Online表单', NULL, NULL, 0, NULL, NULL, 2, 'online:form:add', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2023-09-06 18:49:52', NULL, NULL, 0, 0, '1', 0);
INSERT INTO `sys_permission` (`id`, `parent_id`, `name`, `url`, `component`, `is_route`, `component_name`, `redirect`, `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, `is_leaf`, `keep_alive`, `hidden`, `hide_tab`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `rule_flag`, `status`, `internal_or_external`) VALUES ('1699374404015751169', '1455101470794850305', '修改Online表单', NULL, NULL, 0, NULL, NULL, 2, 'online:form:edit', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2023-09-06 18:50:05', NULL, NULL, 0, 0, '1', 0);

UPDATE `sys_permission` SET `parent_id` = '1455101470794850305', `name` = '选择代码生成路径', `url` = NULL, `component` = NULL, `is_route` = 0, `component_name` = NULL, `redirect` = NULL, `menu_type` = 2, `perms` = 'online:codeGenerate:projectPath', `perms_type` = '1', `sort_no` = NULL, `always_show` = 0, `icon` = NULL, `is_leaf` = 1, `keep_alive` = 0, `hidden` = 0, `hide_tab` = 0, `description` = NULL, `create_by` = 'admin', `create_time` = '2022-07-12 14:03:26', `update_by` = NULL, `update_time` = NULL, `del_flag` = 0, `rule_flag` = 0, `status` = '1', `internal_or_external` = 0 WHERE `id` = '1546736974756032513';
UPDATE `sys_permission` SET `parent_id` = '1455101470794850305', `name` = '同步数据库', `url` = NULL, `component` = NULL, `is_route` = 0, `component_name` = NULL, `redirect` = NULL, `menu_type` = 2, `perms` = 'online:form:syncDb', `perms_type` = '1', `sort_no` = NULL, `always_show` = 0, `icon` = NULL, `is_leaf` = 1, `keep_alive` = 0, `hidden` = 0, `hide_tab` = 0, `description` = NULL, `create_by` = 'admin', `create_time` = '2023-09-06 18:49:33', `update_by` = NULL, `update_time` = NULL, `del_flag` = 0, `rule_flag` = 0, `status` = '1', `internal_or_external` = 0 WHERE `id` = '1699374269152100354';
UPDATE `sys_permission` SET `parent_id` = '1455101470794850305', `name` = '查询数据库表名', `url` = NULL, `component` = NULL, `is_route` = 0, `component_name` = NULL, `redirect` = NULL, `menu_type` = 2, `perms` = 'online:form:queryTables', `perms_type` = '1', `sort_no` = NULL, `always_show` = 0, `icon` = NULL, `is_leaf` = 1, `keep_alive` = 0, `hidden` = 0, `hide_tab` = 0, `description` = NULL, `create_by` = 'admin', `create_time` = '2023-09-06 18:50:31', `update_by` = NULL, `update_time` = NULL, `del_flag` = 0, `rule_flag` = 0, `status` = '1', `internal_or_external` = 0 WHERE `id` = '1699374509749960705';


-- 仪表盘 新增5个模版
-- 公司年度招聘看板
INSERT INTO `onl_drag_page` (`id`, `name`, `path`, `background_color`, `background_image`, `design_type`, `theme`, `style`, `cover_url`, `template`, `protection_code`, `type`, `iz_template`, `create_by`, `create_time`, `update_by`, `update_time`, `low_app_id`, `tenant_id`, `update_count`, `visits_num`) VALUES ('924603858451734528', '公司年度招聘看板', '/drag/page/view/924603858451734528', NULL, NULL, 100, 'default', 'default', NULL, '[{\"pcX\":0,\"moved\":false,\"pcY\":0,\"h\":8,\"i\":\"95729a29-9b15-4102-8ca0-4474bcdfd6ea\",\"orderNum\":0,\"component\":\"JText\",\"w\":24,\"x\":0,\"y\":0,\"pageCompId\":\"925744261272031232\",\"key\":\"c65d42a5-a824-49a9-83c0-c4596aa219d4\"},{\"pcX\":0,\"moved\":false,\"pcY\":13,\"h\":6,\"i\":\"89ac8d65-5a17-4c62-83cc-05f29f629734\",\"orderNum\":13,\"component\":\"JText\",\"w\":3,\"x\":0,\"y\":13,\"pageCompId\":\"925744261330751488\",\"key\":\"45bb9ca9-ade1-44b4-8a8c-f912aa5920b6\"},{\"pcX\":0,\"moved\":false,\"pcY\":19,\"h\":6,\"i\":\"f5f244fb-deed-48eb-8ada-78dadbe08bd8\",\"orderNum\":21,\"component\":\"JText\",\"w\":3,\"x\":0,\"y\":19,\"pageCompId\":\"925744261351723008\",\"key\":\"37d8b7ad-6b59-480f-8af1-c44a03ffca00\"},{\"pcX\":3,\"moved\":false,\"pcY\":13,\"h\":6,\"i\":\"de17aaaf-b81c-48b3-8309-854b1d0b3d14\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":3,\"y\":13,\"pageCompId\":\"925744261381083136\",\"key\":\"41b3d153-7e04-4751-94a3-2f6b466e9424\"},{\"pcX\":3,\"moved\":false,\"pcY\":19,\"h\":6,\"i\":\"2df4f63e-b1a9-4a53-8972-08947f5f011b\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":3,\"y\":19,\"pageCompId\":\"925744261402054656\",\"key\":\"e065a06b-e251-42b3-8ecf-e115459ae476\"},{\"pcX\":6,\"moved\":false,\"pcY\":13,\"h\":16,\"i\":\"2429182c-a58b-4cd9-8c88-8ad9b2b6a1e2\",\"orderNum\":20,\"component\":\"JRing\",\"w\":6,\"x\":6,\"y\":13,\"pageCompId\":\"925744261427220480\",\"key\":\"0bd2ecf1-d948-411c-8c49-09530f9ccae0\"},{\"pcX\":12,\"moved\":false,\"pcY\":13,\"h\":31,\"i\":\"33c42a31-ee7f-4f0a-b9bb-280bef0f4c02\",\"orderNum\":20,\"component\":\"JBubbleMap\",\"w\":6,\"x\":12,\"y\":13,\"pageCompId\":\"925744261464969216\",\"key\":\"6f298d57-db77-4801-9dc8-76b14967300f\"},{\"pcX\":18,\"moved\":false,\"pcY\":13,\"h\":31,\"i\":\"59cce641-ef13-4a49-835b-4ad20a3cc477\",\"orderNum\":20,\"component\":\"JFunnel\",\"w\":6,\"x\":18,\"y\":13,\"pageCompId\":\"925744261490135040\",\"key\":\"e8e525b9-051b-4f45-88ec-00dfcca1ffc8\"},{\"pcX\":0,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"6979473b-9eb1-4d90-887c-483378b89886\",\"orderNum\":20,\"component\":\"JText\",\"w\":6,\"x\":0,\"y\":8,\"pageCompId\":\"925744261519495168\",\"key\":\"79a6e469-54b4-46dd-88f2-896c07e6ec0b\"},{\"pcX\":6,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"fabcd19e-73a2-4a44-9035-d18bccfa77ba\",\"orderNum\":25,\"component\":\"JText\",\"w\":12,\"x\":6,\"y\":8,\"pageCompId\":\"925744261544660992\",\"key\":\"808ca03a-2f22-4f5e-851b-cdd19a2e059d\"},{\"pcX\":18,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"e5eb46cd-4d1a-447e-b42a-6e390fa7080e\",\"orderNum\":25,\"component\":\"JText\",\"w\":6,\"x\":18,\"y\":8,\"pageCompId\":\"925744261569826816\",\"key\":\"8b2edadf-cdaf-43f4-86a1-2735219e5980\"},{\"pcX\":6,\"moved\":false,\"pcY\":29,\"h\":15,\"i\":\"a53c49e6-435a-402d-8de1-b8644b87486b\",\"orderNum\":25,\"component\":\"JPie\",\"w\":6,\"x\":6,\"y\":29,\"pageCompId\":\"925744261594992640\",\"key\":\"e8bdb58d-8950-4791-93dd-37a064e886f4\"},{\"pcX\":0,\"moved\":false,\"pcY\":25,\"h\":19,\"i\":\"f42ce4f9-0e56-47da-8220-254436afea6d\",\"orderNum\":25,\"component\":\"JSmoothLine\",\"w\":6,\"x\":0,\"y\":25,\"pageCompId\":\"925744261615964160\",\"key\":\"891346e4-3bb3-46d0-af0d-da0a6cc118b8\"},{\"component\":\"JText\",\"pcX\":0,\"w\":6,\"moved\":false,\"pcY\":44,\"x\":0,\"h\":5,\"i\":\"102fa102-3bd7-4e86-83ea-77cc7ef23651\",\"y\":44,\"orderNum\":44,\"pageCompId\":\"925744261636935680\"},{\"component\":\"JMultipleBar\",\"pcX\":0,\"w\":6,\"moved\":false,\"pcY\":49,\"x\":0,\"h\":29,\"i\":\"6841deb3-d062-42d8-8a2b-b58f6da64159\",\"y\":49,\"orderNum\":49,\"pageCompId\":\"925744261662101504\"},{\"component\":\"JCommonTable\",\"pcX\":6,\"w\":12,\"moved\":false,\"pcY\":49,\"x\":6,\"h\":29,\"i\":\"92758754-3165-4df6-a897-6da821492f81\",\"y\":49,\"orderNum\":69,\"pageCompId\":\"925744261687267328\"},{\"component\":\"JText\",\"pcX\":6,\"w\":12,\"moved\":false,\"pcY\":44,\"x\":6,\"h\":5,\"i\":\"201a48c1-924c-4da0-87e5-9dc0eb2579a9\",\"y\":44,\"orderNum\":69,\"pageCompId\":\"925744261712433152\"},{\"component\":\"JText\",\"pcX\":18,\"w\":6,\"moved\":false,\"pcY\":44,\"x\":18,\"h\":5,\"i\":\"7e2ecc0a-7930-4c76-9c9a-76af22a58f59\",\"y\":44,\"orderNum\":78,\"pageCompId\":\"925744261745987584\"},{\"component\":\"JStackBar\",\"pcX\":18,\"w\":6,\"moved\":false,\"pcY\":49,\"x\":18,\"h\":29,\"i\":\"a9f3e66d-cfcd-41bb-908b-fc2e95cdb125\",\"y\":49,\"orderNum\":78,\"pageCompId\":\"925744261766959104\"}]', 'amVlY2cxMzE0', '1', '1', 'admin', '2024-03-04 20:12:28', 'admin', '2024-03-13 14:05:31', NULL, 1, 41, 8);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261272031232', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"北京公司2023年度招聘看板\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1604,\"height\":78},\"background\":\"#4A90E2\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":500},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261330751488', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"简历投递数\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#22B6D4\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":24},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261351723008', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"386\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#22B6D4\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":50},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261381083136', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"入职人数\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#69AAF5\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":10,\"marginLeft\":24},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261402054656', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"15\",\"borderColor\":\"#69AAF5\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#4A90E2\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":47},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261427220480', NULL, '924603858451734528', NULL, 'JRing', '{\"chartData\":\"[  {    \\\"value\\\": 121,    \\\"name\\\": \\\"四川省\\\"  },  {    \\\"value\\\": 251,    \\\"name\\\": \\\"山东省\\\"  },  {    \\\"value\\\": 580,    \\\"name\\\": \\\"北京市\\\"  },  {    \\\"value\\\": 484,    \\\"name\\\": \\\"河北省\\\"  },  {    \\\"value\\\": 300,    \\\"name\\\": \\\"河南省\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":166},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"grid\":{\"top\":38,\"left\":52,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"label\":{\"color\":\"#000000\",\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"legend\":{\"show\":false},\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":false,\"text\":\"基础环形图\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261464969216', NULL, '924603858451734528', NULL, 'JBubbleMap', '{\"borderColor\":\"#FFFFFF00\",\"commonOption\":{\"barSize\":10,\"gradientColor\":false,\"breadcrumb\":{\"drillDown\":false,\"textColor\":\"#000000\"},\"areaColor\":{\"color1\":\"#F7F7F7\",\"color2\":\"#fcc02e\"},\"barColor\":\"#fff176\",\"barColor2\":\"#fcc02e\",\"inRange\":{\"color\":[\"#04387b\",\"#467bc0\"]}},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"区域\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"jsConfig\":\"\",\"dataType\":1,\"h\":50,\"activeKey\":1,\"url\":\"http://api.jeecg.com/mock/33/radar\",\"timeOut\":0,\"chartData\":\"[  {    \\\"name\\\": \\\"北京\\\",    \\\"value\\\": 199  },  {    \\\"name\\\": \\\"山东\\\",    \\\"value\\\": 180  },  {    \\\"name\\\": \\\"四川\\\",    \\\"value\\\": 137  },  {    \\\"name\\\": \\\"新疆\\\",    \\\"value\\\": 125  },  {    \\\"name\\\": \\\"河南\\\",    \\\"value\\\": 123  },  {    \\\"name\\\": \\\"广东\\\",    \\\"value\\\": 123  }]\",\"size\":{\"width\":298,\"height\":331},\"background\":\"#FFFFFF\",\"w\":12,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"option\":{\"area\":{\"markerColor\":\"#DDE330\",\"shadowBlur\":10,\"markerCount\":5,\"markerOpacity\":1,\"name\":[\"中国\"],\"scatterLabelShow\":false,\"shadowColor\":\"#DDE330\",\"value\":[\"china\"],\"markerType\":\"effectScatter\"},\"headerBgColor\":\"#FFFFFF\",\"bodyColor\":\"#000000\",\"legend\":{\"data\":[]},\"title\":{\"top\":1,\"left\":10,\"show\":true,\"text\":\"\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"bodyBgColor\":\"#FFFFFF\",\"drillDown\":false,\"geo\":{\"top\":38,\"itemStyle\":{\"normal\":{\"shadowOffsetX\":0,\"borderColor\":\"#A9A9A9\",\"shadowOffsetY\":0,\"areaColor\":\"\",\"shadowBlur\":0,\"borderWidth\":1,\"shadowColor\":\"#80D9F8\"},\"emphasis\":{\"areaColor\":\"#FFF59C\",\"borderWidth\":0}},\"zoom\":1,\"label\":{\"emphasis\":{\"color\":\"#fff\",\"show\":false}},\"roam\":true},\"headerColor\":\"#000000\",\"grid\":{\"bottom\":115,\"show\":false},\"graphic\":[],\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"},\"visualMap\":{\"min\":0,\"top\":\"bottom\",\"max\":200,\"left\":\"5%\",\"calculable\":true,\"show\":false,\"type\":\"continuous\",\"seriesIndex\":[1]}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261490135040', NULL, '924603858451734528', NULL, 'JFunnel', '{\"chartData\":\"[  {    \\\"value\\\": 15,    \\\"name\\\": \\\"入职\\\"  },  {    \\\"value\\\": 5,    \\\"name\\\": \\\"待入职\\\"  },  {    \\\"value\\\": 23,    \\\"name\\\": \\\"一面\\\"  },  {    \\\"value\\\": 10,    \\\"name\\\": \\\"HR沟通\\\"  },  {    \\\"value\\\": 234,    \\\"name\\\": \\\"收到简历\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":331},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/funnel\",\"timeOut\":0,\"option\":{\"grid\":{\"bottom\":115},\"legend\":{\"orient\":\"horizontal\"},\"series\":[{\"left\":\"10%\",\"gap\":2,\"name\":\"Funnel\",\"width\":\"80%\",\"emphasis\":{\"label\":{\"fontSize\":20}},\"itemStyle\":{\"borderColor\":\"#fff\",\"borderWidth\":1},\"sort\":\"descending\",\"label\":{\"show\":true,\"position\":\"inside\"},\"labelLine\":{\"lineStyle\":{\"width\":1,\"type\":\"solid\"},\"length\":10},\"type\":\"funnel\"}],\"tooltip\":{\"formatter\":\"{a} <br/>{b} : {c}%\",\"trigger\":\"item\"},\"title\":{\"show\":false,\"text\":\"基础漏斗图\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261519495168', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"概览\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":8},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261544660992', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"候选人分布\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":605,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":5},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261569826816', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"漏斗图\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261594992640', NULL, '924603858451734528', NULL, 'JPie', '{\"chartData\":\"[  {    \\\"value\\\": 1048,    \\\"name\\\": \\\"双一流大学\\\"  },  {    \\\"value\\\": 735,    \\\"name\\\": \\\"双一流学科\\\"  },  {    \\\"value\\\": 580,    \\\"name\\\": \\\"211\\\"  },  {    \\\"value\\\": 484,    \\\"name\\\": \\\"985\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":155},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"option\":{\"grid\":{\"top\":30,\"left\":46,\"bottom\":115,\"show\":false},\"legend\":{\"r\":1,\"orient\":\"vertical\",\"t\":2,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":false,\"text\":\"基础饼图\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261615964160', NULL, '924603858451734528', NULL, 'JSmoothLine', '{\"chartData\":\"[  {    \\\"value\\\": 10,    \\\"name\\\": \\\"03-12\\\"  },  {    \\\"value\\\": 15,    \\\"name\\\": \\\"03-22\\\"  },  {    \\\"value\\\": 6,    \\\"name\\\": \\\"03-18\\\"  },  {    \\\"value\\\": 19,    \\\"name\\\": \\\"04-12\\\"  },  {    \\\"value\\\": 30,    \\\"name\\\": \\\"05-29\\\"  },  {    \\\"value\\\": 20,    \\\"name\\\": \\\"08-29\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":199},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":33,\"left\":23,\"bottom\":77,\"show\":false,\"right\":25},\"series\":[{\"data\":[],\"type\":\"line\",\"smooth\":true}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"\",\"top\":1,\"left\":1,\"text\":\"简历投递日期分布\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261636935680', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"渠道质量\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":5},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261662101504', NULL, '924603858451734528', NULL, 'JMultipleBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"人才库\\\",    \\\"value\\\": 150,    \\\"type\\\": \\\"投递数\\\"  },  {    \\\"name\\\": \\\"招聘网站\\\",    \\\"value\\\": 269,    \\\"type\\\": \\\"投递数\\\"  },  {    \\\"name\\\": \\\"其他渠道\\\",    \\\"value\\\": 81,    \\\"type\\\": \\\"投递数\\\"  },  {    \\\"name\\\": \\\"内推\\\",    \\\"value\\\": 67,    \\\"type\\\": \\\"投递数\\\"  },  {    \\\"name\\\": \\\"人才库\\\",    \\\"value\\\": 50,    \\\"type\\\": \\\"面试数\\\"  },  {    \\\"name\\\": \\\"招聘网站\\\",    \\\"value\\\": 100,    \\\"type\\\": \\\"面试数\\\"  },  {    \\\"name\\\": \\\"其他渠道\\\",    \\\"value\\\": 10,    \\\"type\\\": \\\"面试数\\\"  },  {    \\\"name\\\": \\\"内推\\\",    \\\"value\\\": 45,    \\\"type\\\": \\\"面试数\\\"  },  {    \\\"name\\\": \\\"人才库\\\",    \\\"value\\\": 13,    \\\"type\\\": \\\"通过数\\\"  },  {    \\\"name\\\": \\\"招聘网站\\\",    \\\"value\\\": 24,    \\\"type\\\": \\\"通过数\\\"  },  {    \\\"name\\\": \\\"其他渠道\\\",    \\\"value\\\": 5,    \\\"type\\\": \\\"通过数\\\"  },  {    \\\"name\\\": \\\"内推\\\",    \\\"value\\\": 25,    \\\"type\\\": \\\"通过数\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":309},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"grid\":{\"top\":29,\"left\":31,\"bottom\":62,\"right\":8},\"series\":[],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":false,\"text\":\"多数据对比柱形图\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261687267328', NULL, '924603858451734528', NULL, 'JCommonTable', '{\"chartData\":\"[  {    \\\"姓名\\\": \\\"陈萌萌\\\",    \\\"筛选简历数\\\": \\\"122\\\",    \\\"推给用人部门\\\": \\\"89\\\",    \\\"面试安排\\\": \\\"40\\\",    \\\"面试通过\\\": \\\"10\\\"  },  {    \\\"姓名\\\": \\\"肖强\\\",    \\\"筛选简历数\\\": \\\"62\\\",    \\\"推给用人部门\\\": \\\"19\\\",    \\\"面试安排\\\": \\\"9\\\",    \\\"面试通过\\\": \\\"1\\\"  },  {    \\\"姓名\\\": \\\"陈晨\\\",    \\\"筛选简历数\\\": \\\"97\\\",    \\\"推给用人部门\\\": \\\"49\\\",    \\\"面试安排\\\": \\\"29\\\",    \\\"面试通过\\\": \\\"15\\\"  },  {    \\\"姓名\\\": \\\"李丽\\\",    \\\"筛选简历数\\\": \\\"47\\\",    \\\"推给用人部门\\\": \\\"29\\\",    \\\"面试安排\\\": \\\"19\\\",    \\\"面试通过\\\": \\\"5\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":605,\"height\":309},\"background\":\"#FFFFFF\",\"w\":24,\"dataType\":1,\"h\":42,\"linkageConfig\":[],\"timeOut\":-1,\"option\":{\"headerBgColor\":\"#50E3C2\",\"headerColor\":\"#7ED321\",\"bodyColor\":\"#000000\",\"bodyBgColor\":\"#FFFFFF\",\"body\":{\"color\":\"#000000\"},\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261712433152', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"招聘工作量细化\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":605,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"bodyColor\":\"#000000\",\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":11},\"bodyBgColor\":\"#FFFFFF\",\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261745987584', NULL, '924603858451734528', NULL, 'JText', '{\"chartData\":\"工作年限对岗位偏好与匹配\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925744261766959104', NULL, '924603858451734528', NULL, 'JStackBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"不满1年\\\",    \\\"value\\\": 150,    \\\"type\\\": \\\"投递数\\\"  },  {    \\\"name\\\": \\\"1-3年\\\",    \\\"value\\\": 269,    \\\"type\\\": \\\"投递数\\\"  },  {    \\\"name\\\": \\\"3-5年\\\",    \\\"value\\\": 81,    \\\"type\\\": \\\"投递数\\\"  },  {    \\\"name\\\": \\\"5-10年\\\",    \\\"value\\\": 67,    \\\"type\\\": \\\"投递数\\\"  },  {    \\\"name\\\": \\\"不满1年\\\",    \\\"value\\\": 50,    \\\"type\\\": \\\"面试数\\\"  },  {    \\\"name\\\": \\\"1-3年\\\",    \\\"value\\\": 100,    \\\"type\\\": \\\"面试数\\\"  },  {    \\\"name\\\": \\\"3-5年\\\",    \\\"value\\\": 10,    \\\"type\\\": \\\"面试数\\\"  },  {    \\\"name\\\": \\\"5-10年\\\",    \\\"value\\\": 45,    \\\"type\\\": \\\"面试数\\\"  },  {    \\\"name\\\": \\\"不满1年\\\",    \\\"value\\\": 13,    \\\"type\\\": \\\"通过数\\\"  },  {    \\\"name\\\": \\\"1-3年\\\",    \\\"value\\\": 24,    \\\"type\\\": \\\"通过数\\\"  },  {    \\\"name\\\": \\\"3-5年\\\",    \\\"value\\\": 5,    \\\"type\\\": \\\"通过数\\\"  },  {    \\\"name\\\": \\\"5-10年\\\",    \\\"value\\\": 25,    \\\"type\\\": \\\"通过数\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":309},\"background\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"customColor\":[{\"color\":\"#7ED321\"},{\"color\":\"#F8E71C\"},{\"color\":\"#F5A623\"}],\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115},\"series\":[],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":false,\"text\":\"堆叠柱形图\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-07 23:44:02', NULL, NULL);

-- 金融封控数据展示
INSERT INTO `onl_drag_page` (`id`, `name`, `path`, `background_color`, `background_image`, `design_type`, `theme`, `style`, `cover_url`, `template`, `protection_code`, `type`, `iz_template`, `create_by`, `create_time`, `update_by`, `update_time`, `low_app_id`, `tenant_id`, `update_count`, `visits_num`) VALUES ('925744661614153728', '金融封控数据展示', '/drag/page/view/925744661614153728', NULL, NULL, 100, 'default', 'default', NULL, '[{\"pcX\":0,\"moved\":false,\"pcY\":0,\"h\":8,\"i\":\"95729a29-9b15-4102-8ca0-4474bcdfd6ea\",\"orderNum\":0,\"component\":\"JText\",\"w\":24,\"x\":0,\"y\":0,\"pageCompId\":\"925962667610906624\",\"key\":\"e39b1e6d-dca7-4223-866f-e2e28f1233da\"},{\"pcX\":9,\"moved\":false,\"pcY\":14,\"h\":6,\"i\":\"89ac8d65-5a17-4c62-83cc-05f29f629734\",\"orderNum\":13,\"component\":\"JText\",\"w\":3,\"x\":9,\"y\":15,\"pageCompId\":\"925962667648655360\",\"key\":\"8c8d6017-40e4-49ae-8acb-74aa3f01d1b8\"},{\"pcX\":9,\"moved\":false,\"pcY\":8,\"h\":7,\"i\":\"f5f244fb-deed-48eb-8ada-78dadbe08bd8\",\"orderNum\":21,\"component\":\"JText\",\"w\":3,\"x\":9,\"y\":8,\"pageCompId\":\"925962667665432576\",\"key\":\"c57cfb37-3333-4fb3-9033-211cc6b63607\"},{\"pcX\":12,\"moved\":false,\"pcY\":14,\"h\":6,\"i\":\"de17aaaf-b81c-48b3-8309-854b1d0b3d14\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":12,\"y\":15,\"pageCompId\":\"925962667682209792\",\"key\":\"e131ddbd-2c02-4c99-b635-2453739987c2\"},{\"pcX\":12,\"moved\":false,\"pcY\":8,\"h\":7,\"i\":\"2df4f63e-b1a9-4a53-8972-08947f5f011b\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":12,\"y\":8,\"pageCompId\":\"925962667698987008\",\"key\":\"650b2a2e-b0c2-4644-8131-d6bfb1d937b7\"},{\"pcX\":9,\"moved\":false,\"pcY\":19,\"h\":6,\"i\":\"6979473b-9eb1-4d90-887c-483378b89886\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":9,\"y\":21,\"pageCompId\":\"925962667715764224\",\"key\":\"11408620-748c-40bb-9cb2-a38e14e099bb\"},{\"pcX\":9,\"moved\":false,\"pcY\":24,\"h\":6,\"i\":\"fabcd19e-73a2-4a44-9035-d18bccfa77ba\",\"orderNum\":25,\"component\":\"JText\",\"w\":3,\"x\":9,\"y\":27,\"pageCompId\":\"925962667732541440\",\"key\":\"ee880781-7379-4234-9fa0-5e838d2f8bc7\"},{\"pcX\":12,\"moved\":false,\"pcY\":19,\"h\":6,\"i\":\"e5eb46cd-4d1a-447e-b42a-6e390fa7080e\",\"orderNum\":25,\"component\":\"JText\",\"w\":3,\"x\":12,\"y\":21,\"pageCompId\":\"925962667753512960\",\"key\":\"55e04041-6770-48f4-905a-2e6296904162\"},{\"pcX\":0,\"moved\":false,\"pcY\":58,\"h\":35,\"i\":\"a53c49e6-435a-402d-8de1-b8644b87486b\",\"orderNum\":25,\"component\":\"JPie\",\"w\":8,\"x\":0,\"y\":57,\"pageCompId\":\"925962667774484480\",\"key\":\"d4994845-fc96-4961-90b3-df812b410b92\"},{\"pcX\":15,\"moved\":false,\"pcY\":8,\"h\":7,\"i\":\"102fa102-3bd7-4e86-83ea-77cc7ef23651\",\"orderNum\":44,\"component\":\"JText\",\"w\":3,\"x\":15,\"y\":8,\"pageCompId\":\"925962667795456000\",\"key\":\"7c8b661f-534e-4ab2-83d9-b845bdb6223f\"},{\"pcX\":15,\"moved\":false,\"pcY\":14,\"h\":6,\"i\":\"201a48c1-924c-4da0-87e5-9dc0eb2579a9\",\"orderNum\":69,\"component\":\"JText\",\"w\":3,\"x\":15,\"y\":15,\"pageCompId\":\"925962667808038912\",\"key\":\"04880935-83e1-41aa-809e-8a1127b1beb3\"},{\"pcX\":12,\"moved\":false,\"pcY\":24,\"h\":6,\"i\":\"7e2ecc0a-7930-4c76-9c9a-76af22a58f59\",\"orderNum\":78,\"component\":\"JText\",\"w\":3,\"x\":12,\"y\":27,\"pageCompId\":\"925962667824816128\",\"key\":\"c208c5a6-fd09-4af5-893e-d678187887eb\"},{\"pcX\":0,\"moved\":false,\"pcY\":8,\"h\":25,\"i\":\"aafdcd2d-abec-48e5-8040-cc237cc70dec\",\"orderNum\":78,\"component\":\"JDynamicBar\",\"w\":9,\"x\":0,\"y\":8,\"pageCompId\":\"925962667841593344\",\"key\":\"a5a17907-3563-4fe6-8081-d4e3eaf0c942\"},{\"pcX\":15,\"moved\":false,\"pcY\":19,\"h\":6,\"i\":\"f5027ec2-646c-4d37-ac73-bd1718720bb6\",\"orderNum\":97,\"component\":\"JText\",\"w\":3,\"x\":15,\"y\":21,\"pageCompId\":\"925962667858370560\",\"key\":\"10b2e5a9-c158-4a78-83c1-bc907c27df9c\"},{\"pcX\":15,\"moved\":false,\"pcY\":24,\"h\":6,\"i\":\"e2996773-4ddf-4f80-9139-4b5edc91c0ff\",\"orderNum\":97,\"component\":\"JText\",\"w\":3,\"x\":15,\"y\":27,\"pageCompId\":\"925962667875147776\",\"key\":\"e5b27b79-7618-42b7-8bb2-6f93102f48e6\"},{\"pcX\":8,\"moved\":false,\"pcY\":29,\"h\":29,\"i\":\"3785fa0d-410c-46d4-8942-d02ddfe884fc\",\"orderNum\":97,\"component\":\"JMultipleLine\",\"w\":10,\"x\":8,\"y\":33,\"pageCompId\":\"925962667896119296\",\"key\":\"cfde0604-580b-4faa-8e45-3922eac1bfd1\"},{\"pcX\":18,\"moved\":false,\"pcY\":38,\"h\":28,\"i\":\"ae1c0095-453d-49ff-8742-252645fd3ca4\",\"orderNum\":97,\"component\":\"JPie\",\"w\":6,\"x\":18,\"y\":38,\"pageCompId\":\"925962667912896512\",\"key\":\"55ff9177-3aa9-4ba4-8628-1da43691633d\"},{\"pcX\":0,\"moved\":false,\"pcY\":34,\"h\":24,\"i\":\"8dede0fc-a267-444a-a8ae-c224bb922d3a\",\"orderNum\":80,\"component\":\"JRing\",\"w\":8,\"x\":0,\"y\":33,\"pageCompId\":\"925962667938062336\",\"key\":\"91ec2e06-a569-489a-8806-08c37b8f2303\"},{\"pcX\":18,\"moved\":false,\"pcY\":8,\"h\":30,\"i\":\"e296cec4-664c-49d7-8922-61cde992b42b\",\"orderNum\":103,\"component\":\"JRing\",\"w\":6,\"x\":18,\"y\":8,\"pageCompId\":\"925962667954839552\",\"key\":\"d7227958-f9b0-400a-89fa-7440b70c3c44\"},{\"component\":\"JDynamicBar\",\"pcX\":8,\"w\":10,\"moved\":false,\"pcY\":58,\"x\":8,\"h\":30,\"i\":\"482a7ccc-51f1-40b5-9204-308be3ecc5f1\",\"y\":62,\"orderNum\":90,\"pageCompId\":\"925962667967422464\"}]', 'amVlY2cxMzE0', '1', '1', 'admin', '2024-03-07 23:45:37', 'admin', '2024-03-13 14:05:28', NULL, 1, 67, 9);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667610906624', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"金融风控数据展示\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1604,\"height\":78},\"background\":\"#4A90E2\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":500},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667648655360', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"复借用户数\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#22B6D4\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":39},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667665432576', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"38611人\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":67},\"background\":\"#22B6D4\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":19,\"marginLeft\":50},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667682209792', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"累计申请人数\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#69AAF5\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":32},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667698987008', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"258909人\",\"borderColor\":\"#69AAF5\",\"size\":{\"width\":192,\"height\":67},\"background\":\"#4A90E2\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":16,\"marginLeft\":45},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667715764224', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"89.5%\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#B05FB3\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":8,\"marginLeft\":63},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667732541440', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"放贷率\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#B05FB3\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":60},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667753512960', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"36.82亿\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#8B572A\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":56},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667774484480', NULL, '925744661614153728', NULL, 'JPie', '{\"chartData\":\"[  {    \\\"value\\\": 1048,    \\\"name\\\": \\\"男\\\"  },  {    \\\"value\\\": 735,    \\\"name\\\": \\\"女\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":375},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"option\":{\"grid\":{\"top\":45,\"left\":43,\"bottom\":115,\"show\":false},\"legend\":{\"r\":1,\"orient\":\"vertical\",\"t\":2,\"show\":true},\"series\":[{\"data\":[],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"男女性别比例\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667795456000', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"214367人\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":67},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":16,\"marginLeft\":46},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667808038912', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"累计放贷用户数\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"bodyColor\":\"#000000\",\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":25},\"bodyBgColor\":\"#FFFFFF\",\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667824816128', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"累计放贷金额\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#8B572A\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":30},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667841593344', NULL, '925744661614153728', NULL, 'JDynamicBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"未到期\\\",    \\\"value\\\": 13100000  },  {    \\\"name\\\": \\\"1-30天\\\",    \\\"value\\\": 14100000  },  {    \\\"name\\\": \\\"31-60天\\\",    \\\"value\\\": 31050000  },  {    \\\"name\\\": \\\"61-90天\\\",    \\\"value\\\": 530000  },  {    \\\"name\\\": \\\"90天以上\\\",    \\\"value\\\": 4100009  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":595,\"height\":265},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":46,\"left\":61,\"bottom\":73,\"right\":40},\"series\":[{\"showBackground\":false,\"backgroundStyle\":{\"color\":\"#51626E\"},\"itemStyle\":{\"color\":\"#EDA737\"}}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"top\":1,\"left\":159,\"text\":\"账龄分布\",\"textStyle\":{\"color\":\"#F5A623\",\"fontSize\":20},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667858370560', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"59.38亿\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#50E3C2\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":54},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667875147776', NULL, '925744661614153728', NULL, 'JText', '{\"chartData\":\"累计申请金额\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#50E3C2\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":28},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667896119296', NULL, '925744661614153728', NULL, 'JMultipleLine', '{\"chartData\":\"[  {    \\\"name\\\": \\\"1月\\\",    \\\"value\\\": 3,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"2月\\\",    \\\"value\\\": 4,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"3月\\\",    \\\"value\\\": 3.5,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"3月\\\",    \\\"value\\\": 5,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"5月\\\",    \\\"value\\\": 4.9,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"6月\\\",    \\\"value\\\": 6,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"7月\\\",    \\\"value\\\": 7,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"8月\\\",    \\\"value\\\": 9,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"9月\\\",    \\\"value\\\": 13,    \\\"type\\\": \\\"2022年月平均\\\"  },  {    \\\"name\\\": \\\"1月\\\",    \\\"value\\\": 6,    \\\"type\\\": \\\"2023年月平均\\\"  },  {    \\\"name\\\": \\\"2月\\\",    \\\"value\\\": 8,    \\\"type\\\": \\\"2023年月平均\\\"  },  {    \\\"name\\\": \\\"3月\\\",    \\\"value\\\": 7,    \\\"type\\\": \\\"2023年月平均\\\"  },  {    \\\"name\\\": \\\"4月\\\",    \\\"value\\\": 10,    \\\"type\\\": \\\"2023年月平均\\\"  },  {    \\\"name\\\": \\\"5月\\\",    \\\"value\\\": 11,    \\\"type\\\": \\\"2023年月平均\\\"  },  {    \\\"name\\\": \\\"6月\\\",    \\\"value\\\": 4,    \\\"type\\\": \\\"2023年月平均\\\"  },  {    \\\"name\\\": \\\"7月\\\",    \\\"value\\\": 20,    \\\"type\\\": \\\"2023年月平均\\\"  },  {    \\\"name\\\": \\\"8月\\\",    \\\"value\\\": 16,    \\\"type\\\": \\\"2023年月平均\\\"  },  {    \\\"name\\\": \\\"9月\\\",    \\\"value\\\": 9,    \\\"type\\\": \\\"2023年月平均\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":663,\"height\":309},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"grid\":{\"top\":90,\"bottom\":115},\"series\":[],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":true,\"text\":\"每月平均申请数\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667912896512', NULL, '925744661614153728', NULL, 'JPie', '{\"chartData\":\"[  {    \\\"value\\\": 1048,    \\\"name\\\": \\\"1年\\\"  },  {    \\\"value\\\": 735,    \\\"name\\\": \\\"1-5年\\\"  },  {    \\\"value\\\": 580,    \\\"name\\\": \\\"5年以上\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":298},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"option\":{\"grid\":{\"top\":50,\"left\":48,\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\",\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"isRadius\":false,\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"贷款期限分布\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667938062336', NULL, '925744661614153728', NULL, 'JRing', '{\"chartData\":\"[  {    \\\"value\\\": 121,    \\\"name\\\": \\\"按时还款\\\"    },  {    \\\"value\\\": 251,    \\\"name\\\": \\\"逾期未还\\\"    },  {    \\\"value\\\": 580,    \\\"name\\\": \\\"逾期已还\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":254},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"label\":{\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"show\":true,\"text\":\"总体放贷情况\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667954839552', NULL, '925744661614153728', NULL, 'JRing', '{\"chartData\":\"[  {    \\\"value\\\": 121,    \\\"name\\\": \\\"可疑\\\"  },  {    \\\"value\\\": 251,    \\\"name\\\": \\\"正常\\\"  },  {    \\\"value\\\": 580,    \\\"name\\\": \\\"不良\\\"  },  {    \\\"value\\\": 80,    \\\"name\\\": \\\"次级\\\"  },  {    \\\"value\\\": 620,    \\\"name\\\": \\\"关注\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":382,\"height\":320},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"label\":{\"show\":false,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"legend\":{\"show\":false},\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"show\":true,\"text\":\"贷款状态跟踪\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925962667967422464', NULL, '925744661614153728', NULL, 'JDynamicBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"高中以下\\\",    \\\"value\\\": 131265  },  {    \\\"name\\\": \\\"大学\\\",    \\\"value\\\": 71410  },  {    \\\"name\\\": \\\"硕士及以上\\\",    \\\"value\\\": 1523  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":663,\"height\":320},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"grid\":{\"top\":54,\"left\":71,\"bottom\":86,\"right\":48},\"series\":[],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"text\":\"贷款人群学历分布\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 14:11:54', NULL, NULL);


-- 车间生产管理
INSERT INTO `onl_drag_page` (`id`, `name`, `path`, `background_color`, `background_image`, `design_type`, `theme`, `style`, `cover_url`, `template`, `protection_code`, `type`, `iz_template`, `create_by`, `create_time`, `update_by`, `update_time`, `low_app_id`, `tenant_id`, `update_count`, `visits_num`) VALUES ('925966805971279872', '车间生产管理', '/drag/page/view/925966805971279872', NULL, NULL, 100, 'default', 'default', NULL, '[{\"pcX\":0,\"moved\":false,\"pcY\":0,\"h\":8,\"i\":\"95729a29-9b15-4102-8ca0-4474bcdfd6ea\",\"orderNum\":0,\"component\":\"JText\",\"w\":24,\"x\":0,\"y\":0,\"pageCompId\":\"925987666560327680\",\"key\":\"24ecfee4-be3c-47e7-a7f7-ee0af996e966\"},{\"pcX\":6,\"moved\":false,\"pcY\":14,\"h\":5,\"i\":\"89ac8d65-5a17-4c62-83cc-05f29f629734\",\"orderNum\":13,\"component\":\"JText\",\"w\":3,\"x\":6,\"y\":14,\"pageCompId\":\"925987666589687808\",\"key\":\"bac10715-7e2f-4408-821e-d300c96bb3f3\"},{\"pcX\":6,\"moved\":false,\"pcY\":8,\"h\":6,\"i\":\"f5f244fb-deed-48eb-8ada-78dadbe08bd8\",\"orderNum\":21,\"component\":\"JText\",\"w\":3,\"x\":6,\"y\":8,\"pageCompId\":\"925987666602270720\",\"key\":\"db0e5681-9481-4eab-b8c5-f7cd25d3310c\"},{\"pcX\":9,\"moved\":false,\"pcY\":14,\"h\":5,\"i\":\"de17aaaf-b81c-48b3-8309-854b1d0b3d14\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":9,\"y\":14,\"pageCompId\":\"925987666619047936\",\"key\":\"fff5d5ab-f352-44ae-8fd7-2f71d761d77a\"},{\"pcX\":9,\"moved\":false,\"pcY\":8,\"h\":6,\"i\":\"2df4f63e-b1a9-4a53-8972-08947f5f011b\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":9,\"y\":8,\"pageCompId\":\"925987666640019456\",\"key\":\"96a44d9e-9d2e-4c8f-ac1d-b5298abe91d9\"},{\"pcX\":18,\"moved\":false,\"pcY\":13,\"h\":31,\"i\":\"59cce641-ef13-4a49-835b-4ad20a3cc477\",\"orderNum\":20,\"component\":\"JFunnel\",\"w\":6,\"x\":18,\"y\":13,\"pageCompId\":\"925987666660990976\",\"key\":\"1f16d847-f33b-4019-80fb-0395a9df514f\"},{\"pcX\":15,\"moved\":false,\"pcY\":14,\"h\":5,\"i\":\"6979473b-9eb1-4d90-887c-483378b89886\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":15,\"y\":14,\"pageCompId\":\"925987666677768192\",\"key\":\"5d942489-aa76-4ef8-8c71-733dd3688266\"},{\"pcX\":12,\"moved\":false,\"pcY\":14,\"h\":5,\"i\":\"fabcd19e-73a2-4a44-9035-d18bccfa77ba\",\"orderNum\":25,\"component\":\"JText\",\"w\":3,\"x\":12,\"y\":14,\"pageCompId\":\"925987666698739712\",\"key\":\"7ed4eaf7-7a75-47c2-925e-43f89b059a02\"},{\"pcX\":12,\"moved\":false,\"pcY\":8,\"h\":6,\"i\":\"e5eb46cd-4d1a-447e-b42a-6e390fa7080e\",\"orderNum\":25,\"component\":\"JText\",\"w\":3,\"x\":12,\"y\":8,\"pageCompId\":\"925987666715516928\",\"key\":\"0dd0c030-aba4-417c-a12e-c5cd32035fad\"},{\"pcX\":18,\"moved\":false,\"pcY\":73,\"h\":24,\"i\":\"f42ce4f9-0e56-47da-8220-254436afea6d\",\"orderNum\":25,\"component\":\"JSmoothLine\",\"w\":6,\"x\":18,\"y\":73,\"pageCompId\":\"925987666732294144\",\"key\":\"2ae30c7d-2c6d-4ef0-8d42-80cea0412320\"},{\"pcX\":15,\"moved\":false,\"pcY\":8,\"h\":6,\"i\":\"102fa102-3bd7-4e86-83ea-77cc7ef23651\",\"orderNum\":44,\"component\":\"JText\",\"w\":3,\"x\":15,\"y\":8,\"pageCompId\":\"925987666749071360\",\"key\":\"f2df4d9f-736f-4064-a404-5e974e84f4ce\"},{\"pcX\":18,\"moved\":false,\"pcY\":44,\"h\":29,\"i\":\"6841deb3-d062-42d8-8a2b-b58f6da64159\",\"orderNum\":49,\"component\":\"JMultipleBar\",\"w\":6,\"x\":18,\"y\":44,\"pageCompId\":\"925987666770042880\",\"key\":\"e96c07b2-5ed1-46dc-894c-202e0b04e6e5\"},{\"pcX\":0,\"moved\":false,\"pcY\":8,\"h\":37,\"i\":\"92758754-3165-4df6-a897-6da821492f81\",\"orderNum\":69,\"component\":\"JCommonTable\",\"w\":6,\"x\":0,\"y\":8,\"pageCompId\":\"925987666786820096\",\"key\":\"b59b15af-0b49-4938-8f87-e2aa7267346f\"},{\"pcX\":6,\"moved\":false,\"pcY\":59,\"h\":5,\"i\":\"201a48c1-924c-4da0-87e5-9dc0eb2579a9\",\"orderNum\":69,\"component\":\"JText\",\"w\":12,\"x\":6,\"y\":59,\"pageCompId\":\"925987666803597312\",\"key\":\"4f4afc8f-4b87-4127-a4f0-c365cce28ede\"},{\"pcX\":18,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"7e2ecc0a-7930-4c76-9c9a-76af22a58f59\",\"orderNum\":78,\"component\":\"JText\",\"w\":6,\"x\":18,\"y\":8,\"pageCompId\":\"925987666828763136\",\"key\":\"8b0234ac-2115-4d19-a356-b67147758ee7\"},{\"pcX\":0,\"moved\":false,\"pcY\":79,\"h\":18,\"i\":\"a9f3e66d-cfcd-41bb-908b-fc2e95cdb125\",\"orderNum\":78,\"component\":\"JStackBar\",\"w\":6,\"x\":0,\"y\":79,\"pageCompId\":\"925987666849734656\",\"key\":\"d85d94cc-642f-4ef7-88c2-12f97e30e636\"},{\"pcX\":0,\"moved\":false,\"pcY\":45,\"h\":34,\"i\":\"e90a0a58-5039-4763-9fd5-e7a271ee7ace\",\"orderNum\":74,\"component\":\"JCommonTable\",\"w\":6,\"x\":0,\"y\":45,\"pageCompId\":\"925987666866511872\",\"key\":\"21a8a9b4-8c86-4b26-8fa2-32104044fb53\"},{\"pcX\":6,\"moved\":false,\"pcY\":19,\"h\":40,\"i\":\"91178230-4e13-448d-bd76-69af0d3defe3\",\"orderNum\":108,\"component\":\"JMixLineBar\",\"w\":12,\"x\":6,\"y\":19,\"pageCompId\":\"925987666879094784\",\"key\":\"40fb5768-5cc1-49d6-b940-ba355c13eb21\"},{\"pcX\":11,\"moved\":false,\"pcY\":64,\"h\":33,\"i\":\"8c7f4e62-f8d9-4ba9-84c9-32fc7ae18206\",\"orderNum\":108,\"component\":\"JCommonTable\",\"w\":7,\"x\":11,\"y\":64,\"pageCompId\":\"925987666904260608\",\"key\":\"75ba8e4b-cced-4b47-afbf-dad8e52a1347\"},{\"component\":\"JPie\",\"pcX\":6,\"w\":5,\"moved\":false,\"pcY\":64,\"x\":6,\"h\":33,\"i\":\"275ce85f-17f5-4d32-8469-386c65a76637\",\"y\":64,\"orderNum\":97,\"pageCompId\":\"925987666925232128\"}]', 'amVlY2cxMzE0', '1', '1', 'admin', '2024-03-08 14:28:20', 'admin', '2024-03-13 14:05:26', NULL, 1, 66, 9);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666560327680', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"XX车间生产管理\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1604,\"height\":78},\"background\":\"#4A90E2\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":500},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666589687808', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"当日产量\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":45},\"background\":\"#22B6D4\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":52},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666602270720', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"15386\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#22B6D4\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":70},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666619047936', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"当月产量\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":45},\"background\":\"#69AAF5\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":44},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666640019456', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"589615\",\"borderColor\":\"#69AAF5\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#4A90E2\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":47},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666660990976', NULL, '925966805971279872', NULL, 'JFunnel', '{\"chartData\":\"[  {    \\\"value\\\": 15,    \\\"name\\\": \\\"生产1部\\\"  },  {    \\\"value\\\": 5,    \\\"name\\\": \\\"生产3部\\\"  },  {    \\\"value\\\": 23,    \\\"name\\\": \\\"生产2部\\\"  },  {    \\\"value\\\": 10,    \\\"name\\\": \\\"生产4部\\\"  },  {    \\\"value\\\": 234,    \\\"name\\\": \\\"生产5部\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":331},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/funnel\",\"timeOut\":0,\"option\":{\"grid\":{\"bottom\":115},\"legend\":{\"orient\":\"horizontal\"},\"series\":[{\"left\":\"10%\",\"gap\":2,\"name\":\"Funnel\",\"width\":\"80%\",\"emphasis\":{\"label\":{\"fontSize\":20}},\"itemStyle\":{\"borderColor\":\"#fff\",\"borderWidth\":1},\"sort\":\"descending\",\"label\":{\"show\":true,\"position\":\"inside\"},\"labelLine\":{\"lineStyle\":{\"width\":1,\"type\":\"solid\"},\"length\":10},\"type\":\"funnel\"}],\"tooltip\":{\"formatter\":\"{a} <br/>{b} : {c}%\",\"trigger\":\"item\"},\"title\":{\"show\":false,\"text\":\"基础漏斗图\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666677768192', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"月投入产出比\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":45},\"background\":\"#A69C32\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":31},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666698739712', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"日目标达成率\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":45},\"background\":\"#61C9B3\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":31},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666715516928', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"86%\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#61C9B3\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":68},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666732294144', NULL, '925966805971279872', NULL, 'JSmoothLine', '{\"chartData\":\"[  {    \\\"value\\\": 60,    \\\"name\\\": \\\"8:00\\\"  },  {    \\\"value\\\": 55,    \\\"name\\\": \\\"10:00\\\"  },  {    \\\"value\\\": 86,    \\\"name\\\": \\\"12:00\\\"  },  {    \\\"value\\\": 70,    \\\"name\\\": \\\"14:00\\\"  },  {    \\\"value\\\": 90,    \\\"name\\\": \\\"16:00\\\"  },  {    \\\"value\\\": 20,    \\\"name\\\": \\\"18:00\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":394,\"height\":254},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":33,\"left\":23,\"bottom\":77,\"show\":false,\"right\":25},\"series\":[{\"data\":[],\"type\":\"line\",\"smooth\":true}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"\",\"top\":1,\"left\":1,\"text\":\"产能效率\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666749071360', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"90%\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#A69C32\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":10,\"marginLeft\":68},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666770042880', NULL, '925966805971279872', NULL, 'JMultipleBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"生产一部\\\",    \\\"value\\\": 150,    \\\"type\\\": \\\"人工成本\\\"  },  {    \\\"name\\\": \\\"生产二部\\\",    \\\"value\\\": 269,    \\\"type\\\": \\\"人工成本\\\"  },  {    \\\"name\\\": \\\"生产三部\\\",    \\\"value\\\": 81,    \\\"type\\\": \\\"人工成本\\\"  },  {    \\\"name\\\": \\\"生产四部\\\",    \\\"value\\\": 67,    \\\"type\\\": \\\"人工成本\\\"  },  {    \\\"name\\\": \\\"生产一部\\\",    \\\"value\\\": 50,    \\\"type\\\": \\\"采购成本\\\"  },  {    \\\"name\\\": \\\"生产二部\\\",    \\\"value\\\": 100,    \\\"type\\\": \\\"采购成本\\\"  },  {    \\\"name\\\": \\\"生产三部\\\",    \\\"value\\\": 10,    \\\"type\\\": \\\"采购成本\\\"  },  {    \\\"name\\\": \\\"生产四部\\\",    \\\"value\\\": 45,    \\\"type\\\": \\\"采购成本\\\"  },  {    \\\"name\\\": \\\"生产一部\\\",    \\\"value\\\": 13,    \\\"type\\\": \\\"生产成本\\\"  },  {    \\\"name\\\": \\\"生产二部\\\",    \\\"value\\\": 24,    \\\"type\\\": \\\"生产成本\\\"  },  {    \\\"name\\\": \\\"生产三部\\\",    \\\"value\\\": 5,    \\\"type\\\": \\\"生产成本\\\"  },  {    \\\"name\\\": \\\"生产四部\\\",    \\\"value\\\": 25,    \\\"type\\\": \\\"生产成本\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":309},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"grid\":{\"top\":29,\"left\":31,\"bottom\":62,\"right\":8},\"series\":[],\"legend\":{\"r\":1},\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":true,\"text\":\"成本控制\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666786820096', NULL, '925966805971279872', NULL, 'JCommonTable', '{\"chartData\":\"[  {    \\\"产品线\\\": \\\"1号产线\\\",    \\\"一次通过数\\\": \\\"122\\\",    \\\"一次未合格\\\": \\\"89\\\"  },  {    \\\"产品线\\\": \\\"2号产线\\\",    \\\"一次通过数\\\": \\\"122\\\",    \\\"一次未合格\\\": \\\"89\\\"  },  {    \\\"产品线\\\": \\\"3号产线\\\",    \\\"一次通过数\\\": \\\"122\\\",    \\\"一次未合格\\\": \\\"89\\\"  },  {    \\\"产品线\\\": \\\"4号产线\\\",    \\\"一次通过数\\\": \\\"122\\\",    \\\"一次未合格\\\": \\\"89\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":394,\"height\":397},\"background\":\"#FFFFFF\",\"w\":24,\"dataType\":1,\"h\":42,\"linkageConfig\":[],\"timeOut\":-1,\"option\":{\"headerBgColor\":\"#50E3C2\",\"headerColor\":\"#7ED321\",\"bodyColor\":\"#000000\",\"bodyBgColor\":\"#FFFFFF\",\"body\":{\"color\":\"#000000\"},\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666803597312', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"设备监控\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":605,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"bodyColor\":\"#000000\",\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":322},\"bodyBgColor\":\"#FFFFFF\",\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666828763136', NULL, '925966805971279872', NULL, 'JText', '{\"chartData\":\"投入产出监控\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":298,\"height\":45},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":124},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666849734656', NULL, '925966805971279872', NULL, 'JStackBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"1号产线\\\",    \\\"value\\\": 10,    \\\"type\\\": \\\"缺勤人数\\\"  },  {    \\\"name\\\": \\\"2号产线\\\",    \\\"value\\\": 8,    \\\"type\\\": \\\"缺勤人数\\\"  },  {    \\\"name\\\": \\\"3号产线\\\",    \\\"value\\\": 10,    \\\"type\\\": \\\"缺勤人数\\\"  },  {    \\\"name\\\": \\\"4号产线\\\",    \\\"value\\\": 4,    \\\"type\\\": \\\"缺勤人数\\\"  },  {    \\\"name\\\": \\\"1号产线\\\",    \\\"value\\\": 35,    \\\"type\\\": \\\"出勤人数\\\"  },  {    \\\"name\\\": \\\"2号产线\\\",    \\\"value\\\": 30,    \\\"type\\\": \\\"出勤人数\\\"  },  {    \\\"name\\\": \\\"3号产线\\\",    \\\"value\\\": 50,    \\\"type\\\": \\\"出勤人数\\\"  },  {    \\\"name\\\": \\\"4号产线\\\",    \\\"value\\\": 45,    \\\"type\\\": \\\"出勤人数\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":394,\"height\":188},\"background\":\"#FFFFFF\",\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"customColor\":[{\"color\":\"#7ED321\"},{\"color\":\"#F8E71C\"},{\"color\":\"#F5A623\"}],\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115},\"series\":[{\"label\":{\"color\":\"#000000\"}}],\"legend\":{\"t\":1},\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"top\":1,\"show\":true,\"text\":\"产线出勤人数\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666866511872', NULL, '925966805971279872', NULL, 'JCommonTable', '{\"chartData\":\"[  {    \\\"订单号\\\": \\\"DD001\\\",    \\\"需求量\\\": \\\"59658\\\",    \\\"库存量\\\": \\\"7895\\\"  },  {    \\\"订单号\\\": \\\"DD002\\\",    \\\"需求量\\\": \\\"4658\\\",    \\\"库存量\\\": \\\"395\\\"  },  {    \\\"订单号\\\": \\\"DD003\\\",    \\\"需求量\\\": \\\"89658\\\",    \\\"库存量\\\": \\\"1895\\\"  },  {    \\\"订单号\\\": \\\"DD004\\\",    \\\"需求量\\\": \\\"39658\\\",    \\\"库存量\\\": \\\"9905\\\"  },  {    \\\"订单号\\\": \\\"DD005\\\",    \\\"需求量\\\": \\\"79658\\\",    \\\"库存量\\\": \\\"7895\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":394,\"height\":364},\"background\":\"#FFFFFF\",\"w\":24,\"dataType\":1,\"h\":42,\"linkageConfig\":[],\"timeOut\":-1,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666879094784', NULL, '925966805971279872', NULL, 'JMixLineBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"1号产品\\\",    \\\"value\\\": 110,    \\\"type\\\": \\\"通过质检数\\\"  },  {    \\\"name\\\": \\\"2号产品\\\",    \\\"value\\\": 130,    \\\"type\\\": \\\"通过质检数\\\"  },  {    \\\"name\\\": \\\"3号产品\\\",    \\\"value\\\": 113.5,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"4号产品\\\",    \\\"value\\\": 150,    \\\"type\\\": \\\"通过质检数\\\"  },  {    \\\"name\\\": \\\"5号产品\\\",    \\\"value\\\": 240.9,    \\\"type\\\": \\\"通过质检数\\\"  },  {    \\\"name\\\": \\\"6号产品\\\",    \\\"value\\\": 160,    \\\"type\\\": \\\"通过质检数\\\"  },  {    \\\"name\\\": \\\"7号产品\\\",    \\\"value\\\": 97,    \\\"type\\\": \\\"通过质检数\\\"  },  {    \\\"name\\\": \\\"8号产品\\\",    \\\"value\\\": 290,    \\\"type\\\": \\\"通过质检数\\\"  },  {    \\\"name\\\": \\\"9号产品\\\",    \\\"value\\\": 230,    \\\"type\\\": \\\"通过质检数\\\"  },  {    \\\"name\\\": \\\"1号产品\\\",    \\\"value\\\": 133,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"2号产品\\\",    \\\"value\\\": 235,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"3号产品\\\",    \\\"value\\\": 187,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"4号产品\\\",    \\\"value\\\": 35,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"5号产品\\\",    \\\"value\\\": 69,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"6号产品\\\",    \\\"value\\\": 100,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"7号产品\\\",    \\\"value\\\": 37,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"8号产品\\\",    \\\"value\\\": 20,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"9号产品\\\",    \\\"value\\\": 33,    \\\"type\\\": \\\"未通过质检数\\\"  },  {    \\\"name\\\": \\\"1号产品\\\",    \\\"value\\\": 12,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"2号产品\\\",    \\\"value\\\": 5,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"3号产品\\\",    \\\"value\\\": 20,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"4号产品\\\",    \\\"value\\\": 15,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"5号产品\\\",    \\\"value\\\": 24,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"6号产品\\\",    \\\"value\\\": 16,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"7号产品\\\",    \\\"value\\\": 9,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"8号产品\\\",    \\\"value\\\": 29,    \\\"type\\\": \\\"通过率\\\"  },  {    \\\"name\\\": \\\"9号产品\\\",    \\\"value\\\": 23,    \\\"type\\\": \\\"通过率\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":797,\"height\":430},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"seriesType\":[{\"series\":\"通过质检数\",\"type\":\"bar\"},{\"series\":\"通过率\",\"type\":\"bar\"},{\"series\":\"未通过质检数\",\"type\":\"line\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"bodyColor\":\"#000000\",\"grid\":{\"top\":90,\"bottom\":115},\"series\":[],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":true,\"text\":\"产量与不良率统计\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"bodyBgColor\":\"#FFFFFF\",\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666904260608', NULL, '925966805971279872', NULL, 'JCommonTable', '{\"chartData\":\"[  {    \\\"设备编号\\\": \\\"HT001\\\",    \\\"产品线\\\": \\\"6号产线\\\",    \\\"开始时间\\\": \\\"2023-10-24\\\",    \\\"产量损失\\\": \\\"1589\\\",    \\\"影响工时\\\": \\\"8\\\"  },  {    \\\"设备编号\\\": \\\"HT589\\\",    \\\"产品线\\\": \\\"4号产线\\\",    \\\"开始时间\\\": \\\"2023-11-14\\\",    \\\"产量损失\\\": \\\"89\\\",    \\\"影响工时\\\": \\\"3\\\"  },  {    \\\"设备编号\\\": \\\"HT008\\\",    \\\"产品线\\\": \\\"1号产线\\\",    \\\"开始时间\\\": \\\"2023-12-24\\\",    \\\"产量损失\\\": \\\"15\\\",    \\\"影响工时\\\": \\\"1\\\"  },  {    \\\"设备编号\\\": \\\"HT0034\\\",    \\\"产品线\\\": \\\"6号产线\\\",    \\\"开始时间\\\": \\\"2023-10-24\\\",    \\\"产量损失\\\": \\\"1589\\\",    \\\"影响工时\\\": \\\"10\\\"  },  {    \\\"设备编号\\\": \\\"HT0011\\\",    \\\"产品线\\\": \\\"6号产线\\\",    \\\"开始时间\\\": \\\"2023-10-24\\\",    \\\"产量损失\\\": \\\"3589\\\",    \\\"影响工时\\\": \\\"16\\\"  },  {    \\\"设备编号\\\": \\\"HT00113\\\",    \\\"产品线\\\": \\\"7号产线\\\",    \\\"开始时间\\\": \\\"2023-11-24\\\",    \\\"产量损失\\\": \\\"1589\\\",    \\\"影响工时\\\": \\\"8\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":461,\"height\":353},\"background\":\"#FFFFFF\",\"w\":24,\"dataType\":1,\"h\":42,\"linkageConfig\":[],\"timeOut\":-1,\"option\":{}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('925987666925232128', NULL, '925966805971279872', NULL, 'JPie', '{\"chartData\":\"[  {    \\\"value\\\": 1048,    \\\"name\\\": \\\"高负荷\\\"  },  {    \\\"value\\\": 735,    \\\"name\\\": \\\"检修\\\"  },  {    \\\"value\\\": 580,    \\\"name\\\": \\\"低速\\\"  },  {    \\\"value\\\": 484,    \\\"name\\\": \\\"正常\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":326,\"height\":353},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"option\":{\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\"},\"series\":[{\"data\":[],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"tooltip\":{\"trigger\":\"item\"},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":false,\"text\":\"基础饼图\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-08 15:51:14', NULL, NULL);


-- 医美行业网络关注度
INSERT INTO `onl_drag_page` (`id`, `name`, `path`, `background_color`, `background_image`, `design_type`, `theme`, `style`, `cover_url`, `template`, `protection_code`, `type`, `iz_template`, `create_by`, `create_time`, `update_by`, `update_time`, `low_app_id`, `tenant_id`, `update_count`, `visits_num`) VALUES ('925988240575995904', '医美行业网络关注度', '/drag/page/view/925988240575995904', NULL, NULL, 100, 'default', 'default', NULL, '[{\"pcX\":0,\"moved\":false,\"pcY\":0,\"h\":8,\"i\":\"95729a29-9b15-4102-8ca0-4474bcdfd6ea\",\"orderNum\":0,\"component\":\"JText\",\"w\":24,\"x\":0,\"y\":0,\"pageCompId\":\"927766205680885760\",\"key\":\"d55088fd-e43d-4a1d-9b05-5649a8eb596c\"},{\"pcX\":0,\"moved\":false,\"pcY\":78,\"h\":6,\"i\":\"89ac8d65-5a17-4c62-83cc-05f29f629734\",\"orderNum\":13,\"component\":\"JText\",\"w\":8,\"x\":0,\"y\":78,\"pageCompId\":\"927766205706051584\",\"key\":\"1c4e0fa2-b239-4f6e-a421-da3e0025d890\"},{\"pcX\":16,\"moved\":false,\"pcY\":77,\"h\":6,\"i\":\"f5f244fb-deed-48eb-8ada-78dadbe08bd8\",\"orderNum\":21,\"component\":\"JText\",\"w\":8,\"x\":16,\"y\":78,\"pageCompId\":\"927766205718634496\",\"key\":\"a97e4d8e-1a35-4259-8450-602159ee4549\"},{\"pcX\":8,\"moved\":false,\"pcY\":78,\"h\":6,\"i\":\"de17aaaf-b81c-48b3-8309-854b1d0b3d14\",\"orderNum\":20,\"component\":\"JText\",\"w\":8,\"x\":8,\"y\":78,\"pageCompId\":\"927766205731217408\",\"key\":\"19e0a67a-1f13-4af0-83ad-e6b8fde95a4f\"},{\"pcX\":16,\"moved\":false,\"pcY\":13,\"h\":30,\"i\":\"59cce641-ef13-4a49-835b-4ad20a3cc477\",\"orderNum\":20,\"component\":\"JFunnel\",\"w\":8,\"x\":16,\"y\":13,\"pageCompId\":\"927766205743800320\",\"key\":\"5d922cdd-ec89-4e9a-964f-c1ae3eedb607\"},{\"pcX\":0,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"6979473b-9eb1-4d90-887c-483378b89886\",\"orderNum\":20,\"component\":\"JText\",\"w\":8,\"x\":0,\"y\":8,\"pageCompId\":\"927766205756383232\",\"key\":\"4aaeb68b-8d14-48ba-89fc-4d22c13adaae\"},{\"pcX\":8,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"fabcd19e-73a2-4a44-9035-d18bccfa77ba\",\"orderNum\":25,\"component\":\"JText\",\"w\":8,\"x\":8,\"y\":8,\"pageCompId\":\"927766205773160448\",\"key\":\"3cf7deaa-fb9d-4ef2-8cd7-d32d0a785690\"},{\"pcX\":16,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"e5eb46cd-4d1a-447e-b42a-6e390fa7080e\",\"orderNum\":25,\"component\":\"JText\",\"w\":8,\"x\":16,\"y\":8,\"pageCompId\":\"927766205794131968\",\"key\":\"88d4fdcd-ff1c-4d46-8466-200ef9e65a69\"},{\"pcX\":8,\"moved\":false,\"pcY\":13,\"h\":30,\"i\":\"f42ce4f9-0e56-47da-8220-254436afea6d\",\"orderNum\":25,\"component\":\"JSmoothLine\",\"w\":8,\"x\":8,\"y\":13,\"pageCompId\":\"927766205806714880\",\"key\":\"5e423a51-6773-44a3-88e3-34b9589c696c\"},{\"pcX\":0,\"moved\":false,\"pcY\":43,\"h\":5,\"i\":\"102fa102-3bd7-4e86-83ea-77cc7ef23651\",\"orderNum\":44,\"component\":\"JText\",\"w\":8,\"x\":0,\"y\":43,\"pageCompId\":\"927766205823492096\",\"key\":\"344516b7-8a19-4cd5-85f8-398c3480f814\"},{\"pcX\":16,\"moved\":false,\"pcY\":48,\"h\":30,\"i\":\"6841deb3-d062-42d8-8a2b-b58f6da64159\",\"orderNum\":49,\"component\":\"JMultipleBar\",\"w\":8,\"x\":16,\"y\":48,\"pageCompId\":\"927766205836075008\",\"key\":\"6d8a2052-2429-418f-80ab-e9e78af70de0\"},{\"pcX\":8,\"moved\":false,\"pcY\":43,\"h\":5,\"i\":\"201a48c1-924c-4da0-87e5-9dc0eb2579a9\",\"orderNum\":69,\"component\":\"JText\",\"w\":8,\"x\":8,\"y\":43,\"pageCompId\":\"927766205852852224\",\"key\":\"b499b520-7880-4630-b223-b0d8b5aed79d\"},{\"pcX\":16,\"moved\":false,\"pcY\":43,\"h\":5,\"i\":\"7e2ecc0a-7930-4c76-9c9a-76af22a58f59\",\"orderNum\":78,\"component\":\"JText\",\"w\":8,\"x\":16,\"y\":43,\"pageCompId\":\"927766205865435136\",\"key\":\"89b201f0-871e-4ec8-96b0-eb03c9b31357\"},{\"pcX\":0,\"moved\":false,\"pcY\":13,\"h\":30,\"i\":\"88e40b11-fc48-4d59-8ba8-5d106f3dfa2b\",\"orderNum\":53,\"component\":\"JMixLineBar\",\"w\":8,\"x\":0,\"y\":13,\"pageCompId\":\"927766205873823744\",\"key\":\"0d8ade3b-02e8-4a6b-a0ed-f0dd46ebe87f\"},{\"pcX\":0,\"moved\":false,\"pcY\":48,\"h\":30,\"i\":\"000017d9-054b-492f-9327-537c64b25f0f\",\"orderNum\":54,\"component\":\"JDynamicBar\",\"w\":8,\"x\":0,\"y\":48,\"pageCompId\":\"927766205890600960\",\"key\":\"64b531f1-56d4-49ad-9afa-e03bd45d0c8b\"},{\"pcX\":0,\"moved\":false,\"pcY\":84,\"h\":29,\"i\":\"0331fad4-d098-4aa4-84e1-0583a0ed828f\",\"orderNum\":80,\"component\":\"JDynamicBar\",\"w\":8,\"x\":0,\"y\":84,\"pageCompId\":\"927766205903183872\",\"key\":\"7f656710-b951-4dce-860b-b5421fc62070\"},{\"pcX\":8,\"moved\":false,\"pcY\":48,\"h\":30,\"i\":\"f36d52f6-942b-438b-881a-2fd02ebfd7f0\",\"orderNum\":109,\"component\":\"JRing\",\"w\":8,\"x\":8,\"y\":48,\"pageCompId\":\"927766205915766784\",\"key\":\"423d760e-342e-473b-8af1-67e1c538c725\"},{\"pcX\":8,\"moved\":false,\"pcY\":84,\"h\":29,\"i\":\"7c994b82-cde8-41a2-9192-21f8c92085b9\",\"orderNum\":109,\"component\":\"JCommonTable\",\"w\":8,\"x\":8,\"y\":84,\"pageCompId\":\"927766205924155392\",\"key\":\"d4c825d1-c807-47f1-ac75-452de7f492bd\"},{\"component\":\"JRing\",\"pcX\":16,\"w\":8,\"moved\":false,\"pcY\":83,\"x\":16,\"h\":29,\"i\":\"7759c7d3-2ea1-4f89-9c49-9866e9d6cc77\",\"y\":84,\"orderNum\":113,\"pageCompId\":\"927766205936738304\"}]', 'amVlY2cxMzE0', '1', '1', 'admin', '2024-03-08 15:53:31', 'admin', '2024-03-13 14:05:24', NULL, 1, 73, 6);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205680885760', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"2023年度医美行业网络关注度\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1604,\"height\":78},\"background\":\"#23BAD9\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"bodyColor\":\"#000000\",\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":500},\"bodyBgColor\":\"#FFFFFF\",\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205706051584', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"公立整形外科热度指数TOP10美誉度\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":56},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"bodyColor\":\"#000000\",\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":24},\"bodyBgColor\":\"#FFFFFF\",\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205718634496', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"敏感信息与非敏感信息占比\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":56},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":11},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205731217408', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"医美行业事件\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":56},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"bodyColor\":\"#000000\",\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":10,\"marginLeft\":24},\"bodyBgColor\":\"#FFFFFF\",\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205743800320', NULL, '925988240575995904', NULL, 'JFunnel', '{\"chartData\":\"[  {    \\\"value\\\": 15,    \\\"name\\\": \\\"美白针\\\"  },  {    \\\"value\\\": 5,    \\\"name\\\": \\\"光子嫩肤\\\"  },  {    \\\"value\\\": 23,    \\\"name\\\": \\\"半永久纹眉\\\"  },  {    \\\"value\\\": 10,    \\\"name\\\": \\\"果酸换肤\\\"  },  {    \\\"value\\\": 234,    \\\"name\\\": \\\"全瓷牙\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":320},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/funnel\",\"timeOut\":0,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"bottom\":115},\"legend\":{\"orient\":\"horizontal\"},\"series\":[{\"left\":\"10%\",\"gap\":2,\"name\":\"Funnel\",\"width\":\"80%\",\"emphasis\":{\"label\":{\"fontSize\":20}},\"itemStyle\":{\"borderColor\":\"#fff\",\"borderWidth\":1},\"sort\":\"descending\",\"label\":{\"color\":\"#787575\",\"show\":true,\"position\":\"inside\"},\"labelLine\":{\"lineStyle\":{\"width\":1,\"type\":\"solid\"},\"length\":10},\"type\":\"funnel\"}],\"tooltip\":{\"formatter\":\"{a} <br/>{b} : {c}%\",\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":false,\"text\":\"基础漏斗图\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205756383232', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"非公立连锁医美机构热度指数TOP10\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":45},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":8},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205773160448', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"医美行业全网信息量走势\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":45},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":5},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205794131968', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"医美项目热度\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":45},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":8},\"title\":{\"subtextStyle\":{\"color\":\"#464646\"},\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205806714880', NULL, '925988240575995904', NULL, 'JSmoothLine', '{\"chartData\":\"[  {    \\\"value\\\": 10,    \\\"name\\\": \\\"1月\\\"  },  {    \\\"value\\\": 15,    \\\"name\\\": \\\"2月\\\"  },  {    \\\"value\\\": 6,    \\\"name\\\": \\\"3月\\\"  },  {    \\\"value\\\": 19,    \\\"name\\\": \\\"4月\\\"  },  {    \\\"value\\\": 30,    \\\"name\\\": \\\"5月\\\"  },  {    \\\"value\\\": 20,    \\\"name\\\": \\\"6月\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":320},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":33,\"left\":23,\"bottom\":77,\"show\":false,\"right\":25},\"series\":[{\"data\":[],\"type\":\"line\",\"smooth\":true}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"\",\"top\":1,\"left\":1,\"show\":false,\"text\":\"\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205823492096', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"公立整形外科热度指数TOP10\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":45},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":5},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205836075008', NULL, '925988240575995904', NULL, 'JMultipleBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"19岁以下\\\",    \\\"value\\\": 150,    \\\"type\\\": \\\"光子嫩肤\\\"  },  {    \\\"name\\\": \\\"20-29岁\\\",    \\\"value\\\": 269,    \\\"type\\\": \\\"光子嫩肤\\\"  },  {    \\\"name\\\": \\\"30-39岁\\\",    \\\"value\\\": 81,    \\\"type\\\": \\\"光子嫩肤\\\"  },  {    \\\"name\\\": \\\"39岁以上\\\",    \\\"value\\\": 67,    \\\"type\\\": \\\"光子嫩肤\\\"  },  {    \\\"name\\\": \\\"19岁以下\\\",    \\\"value\\\": 50,    \\\"type\\\": \\\"热玛吉\\\"  },  {    \\\"name\\\": \\\"20-29岁\\\",    \\\"value\\\": 100,    \\\"type\\\": \\\"热玛吉\\\"  },  {    \\\"name\\\": \\\"30-39岁\\\",    \\\"value\\\": 10,    \\\"type\\\": \\\"热玛吉\\\"  },  {    \\\"name\\\": \\\"39岁以上\\\",    \\\"value\\\": 45,    \\\"type\\\": \\\"热玛吉\\\"  },  {    \\\"name\\\": \\\"19岁以下\\\",    \\\"value\\\": 13,    \\\"type\\\": \\\"水光针\\\"  },  {    \\\"name\\\": \\\"20-29岁\\\",    \\\"value\\\": 24,    \\\"type\\\": \\\"水光针\\\"  },  {    \\\"name\\\": \\\"30-39岁\\\",    \\\"value\\\": 5,    \\\"type\\\": \\\"水光针\\\"  },  {    \\\"name\\\": \\\"39岁以上\\\",    \\\"value\\\": 25,    \\\"type\\\": \\\"水光针\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":320},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":29,\"left\":31,\"bottom\":62,\"right\":8},\"series\":[{\"label\":{\"color\":\"#787575\"}}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":false,\"text\":\"多数据对比柱形图\",\"subtextStyle\":{\"color\":\"#464646\"},\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205852852224', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"医美行业热点事件TOP50分类\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":45},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"bodyColor\":\"#000000\",\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":11},\"bodyBgColor\":\"#FFFFFF\",\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205865435136', NULL, '925988240575995904', NULL, 'JText', '{\"chartData\":\"各年龄段对医美项目TOP3关注度对比\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":45},\"background\":\"#FFFFFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#23BAD9\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205873823744', NULL, '925988240575995904', NULL, 'JMixLineBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"华美美容院\\\",    \\\"value\\\": 10,    \\\"type\\\": \\\"传播热度指数\\\"  },  {    \\\"name\\\": \\\"美莱美容院\\\",    \\\"value\\\": 9,    \\\"type\\\": \\\"传播热度指数\\\"  },  {    \\\"name\\\": \\\"爱美美容院\\\",    \\\"value\\\": 5,    \\\"type\\\": \\\"传播热度指数\\\"  },  {    \\\"name\\\": \\\"一星美容院\\\",    \\\"value\\\": 9,    \\\"type\\\": \\\"传播热度指数\\\"  },  {    \\\"name\\\": \\\"慧美美容院\\\",    \\\"value\\\": 2,    \\\"type\\\": \\\"传播热度指数\\\"  },  {    \\\"name\\\": \\\"华美美容院\\\",    \\\"value\\\": 33,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"美莱美容院\\\",    \\\"value\\\": 35,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"爱美美容院\\\",    \\\"value\\\": 37,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"一星美容院\\\",    \\\"value\\\": 35,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"慧美美容院\\\",    \\\"value\\\": 34.9,    \\\"type\\\": \\\"美誉度\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":320},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"seriesType\":[{\"series\":\"传播热度指数\",\"type\":\"bar\"},{\"series\":\"美誉度\",\"type\":\"line\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"rotate\":-33,\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":90,\"bottom\":115},\"series\":[],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":false,\"text\":\"折柱图\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205890600960', NULL, '925988240575995904', NULL, 'JDynamicBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"江苏人民医院\\\",    \\\"value\\\": 55,    \\\"type\\\": \\\"传播热度\\\"  },  {    \\\"name\\\": \\\"上海人民医院\\\",    \\\"value\\\": 14,    \\\"type\\\": \\\"传播热度\\\"  },  {    \\\"name\\\": \\\"北京大学第一医院\\\",    \\\"value\\\": 31.5,    \\\"type\\\": \\\"传播热度\\\"  },  {    \\\"name\\\": \\\"北京朝阳医院\\\",    \\\"value\\\": 53,    \\\"type\\\": \\\"传播热度\\\"  },  {    \\\"name\\\": \\\"北京协和医院\\\",    \\\"value\\\": 41.9,    \\\"type\\\": \\\"传播热度\\\"  },  {    \\\"name\\\": \\\"八大处医院\\\",    \\\"value\\\": 61,    \\\"type\\\": \\\"传播热度\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":320},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":8,\"left\":113,\"bottom\":71,\"right\":59},\"series\":[{\"barWidth\":17,\"itemStyle\":{\"color\":\"#F099E1EE\"},\"label\":{\"color\":\"#000000\"}}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":false,\"text\":\"动态柱形图\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205903183872', NULL, '925988240575995904', NULL, 'JDynamicBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"江苏人民医院\\\",    \\\"value\\\": 88,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"上海人民医院\\\",    \\\"value\\\": 76,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"北京大学第一医院\\\",    \\\"value\\\": 92,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"北京朝阳医院\\\",    \\\"value\\\": 53,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"北京协和医院\\\",    \\\"value\\\": 90,    \\\"type\\\": \\\"美誉度\\\"  },  {    \\\"name\\\": \\\"八大处医院\\\",    \\\"value\\\": 98.7,    \\\"type\\\": \\\"美誉度\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":309},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"分组\"},{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/26/stackedBar\",\"timeOut\":0,\"option\":{\"grid\":{\"top\":19,\"left\":109,\"bottom\":60,\"right\":31},\"series\":[{\"barWidth\":18,\"itemStyle\":{\"color\":\"#50E3C2\"}}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":false,\"text\":\"动态柱形图\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205915766784', NULL, '925988240575995904', NULL, 'JRing', '{\"chartData\":\"[  {    \\\"value\\\": 46,    \\\"name\\\": \\\"涉机构事件\\\"  },  {    \\\"value\\\": 54,    \\\"name\\\": \\\"涉行业事件\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":320},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"customColor\":[{\"color\":\"#F099E1EE\"},{\"color\":\"#23BAD9\"}],\"grid\":{\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"itemStyle\":{\"color\":\"#64B5F6\"},\"label\":{\"color\":\"#000000\",\"show\":true,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"legend\":{\"r\":26},\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":false,\"text\":\"基础环形图\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205924155392', NULL, '925988240575995904', NULL, 'JCommonTable', '{\"chartData\":\"[  {    \\\"医美行业热点事件\\\": \\\"FDA批准新一代A型肉毒素上市\\\",    \\\"传播热度指数\\\": 6.25  },  {    \\\"医美行业热点事件\\\": \\\"多地医保局发布口腔种植收费调查登记的通知\\\",    \\\"传播热度指数\\\": 5.3  },  {    \\\"医美行业热点事件\\\": \\\"种植牙服务价格下调\\\",    \\\"传播热度指数\\\": 4  },  {    \\\"医美行业热点事件\\\": \\\"消保委2023年受理消费者投诉41万余件 涉及医美等多个方面\\\",    \\\"传播热度指数\\\": 7.23  },  {    \\\"医美行业热点事件\\\": \\\"轻医美-收费不透明\\\",    \\\"传播热度指数\\\": 3.6  },  {    \\\"医美行业热点事件\\\": \\\"专家提醒:低价医美有风险\\\",    \\\"传播热度指数\\\": 2  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":309},\"background\":\"#FFFFFF\",\"w\":24,\"dataType\":1,\"h\":42,\"linkageConfig\":[],\"timeOut\":-1,\"option\":{\"headerBgColor\":\"#C1EEF7\",\"headerColor\":\"#417505\",\"bodyColor\":\"#000000\",\"bodyBgColor\":\"#FFFFFF\",\"body\":{\"color\":\"#000000\"},\"title\":{\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927766205936738304', NULL, '925988240575995904', NULL, 'JRing', '{\"chartData\":\"[  {    \\\"value\\\": 4.61,    \\\"name\\\": \\\"敏感\\\"  },  {    \\\"value\\\": 95.39,    \\\"name\\\": \\\"非敏感\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":309},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"customColor\":[{\"color\":\"#F099E1EE\"},{\"color\":\"#6AD5EB\"}],\"grid\":{\"top\":45,\"left\":42,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"itemStyle\":{\"color\":\"#64B5F6\"},\"label\":{\"color\":\"#000000\",\"show\":true,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":false,\"text\":\"基础环形图\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 13:38:31', NULL, NULL);


-- 消费者权益保护
INSERT INTO `onl_drag_page` (`id`, `name`, `path`, `background_color`, `background_image`, `design_type`, `theme`, `style`, `cover_url`, `template`, `protection_code`, `type`, `iz_template`, `create_by`, `create_time`, `update_by`, `update_time`, `low_app_id`, `tenant_id`, `update_count`, `visits_num`) VALUES ('925988379923357696', '消费者权益保护', '/drag/page/view/925988379923357696', NULL, NULL, 100, 'default', 'default', NULL, '[{\"pcX\":0,\"moved\":false,\"pcY\":0,\"h\":8,\"i\":\"95729a29-9b15-4102-8ca0-4474bcdfd6ea\",\"orderNum\":0,\"component\":\"JText\",\"w\":24,\"x\":0,\"y\":0,\"pageCompId\":\"927733693940277248\",\"key\":\"3dce2b44-02cb-4dce-83ce-c584eaba0bae\"},{\"pcX\":0,\"moved\":false,\"pcY\":8,\"h\":6,\"i\":\"89ac8d65-5a17-4c62-83cc-05f29f629734\",\"orderNum\":13,\"component\":\"JText\",\"w\":3,\"x\":0,\"y\":8,\"pageCompId\":\"927733693998997504\",\"key\":\"8ecd434b-858a-451a-8df5-236157d63c21\"},{\"pcX\":0,\"moved\":false,\"pcY\":14,\"h\":6,\"i\":\"f5f244fb-deed-48eb-8ada-78dadbe08bd8\",\"orderNum\":21,\"component\":\"JText\",\"w\":3,\"x\":0,\"y\":14,\"pageCompId\":\"927733694024163328\",\"key\":\"9bbaae8e-f7ea-41f7-be93-47039647c615\"},{\"pcX\":3,\"moved\":false,\"pcY\":8,\"h\":6,\"i\":\"de17aaaf-b81c-48b3-8309-854b1d0b3d14\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":3,\"y\":8,\"pageCompId\":\"927733694045134848\",\"key\":\"afbf3bce-23f1-4127-bb5a-b211e27b8593\"},{\"pcX\":3,\"moved\":false,\"pcY\":14,\"h\":6,\"i\":\"2df4f63e-b1a9-4a53-8972-08947f5f011b\",\"orderNum\":20,\"component\":\"JText\",\"w\":3,\"x\":3,\"y\":14,\"pageCompId\":\"927733694066106368\",\"key\":\"3ec0dc1e-73b0-4196-8afd-c715f3a8ada9\"},{\"pcX\":6,\"moved\":false,\"pcY\":36,\"h\":5,\"i\":\"6979473b-9eb1-4d90-887c-483378b89886\",\"orderNum\":20,\"component\":\"JText\",\"w\":8,\"x\":6,\"y\":36,\"pageCompId\":\"927733694087077888\",\"key\":\"ac2978bf-43af-4b8a-8b71-af89857ea47e\"},{\"pcX\":6,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"fabcd19e-73a2-4a44-9035-d18bccfa77ba\",\"orderNum\":25,\"component\":\"JText\",\"w\":8,\"x\":6,\"y\":8,\"pageCompId\":\"927733694099660800\",\"key\":\"c53a541d-9a9c-4b1f-ba18-f55df961fc4e\"},{\"pcX\":14,\"moved\":false,\"pcY\":8,\"h\":5,\"i\":\"e5eb46cd-4d1a-447e-b42a-6e390fa7080e\",\"orderNum\":25,\"component\":\"JText\",\"w\":10,\"x\":14,\"y\":8,\"pageCompId\":\"927733694116438016\",\"key\":\"a8ad0233-9683-4d1b-9e1b-e33cf79c1179\"},{\"pcX\":14,\"moved\":false,\"pcY\":24,\"h\":25,\"i\":\"a53c49e6-435a-402d-8de1-b8644b87486b\",\"orderNum\":25,\"component\":\"JPie\",\"w\":5,\"x\":14,\"y\":24,\"pageCompId\":\"927733694141603840\",\"key\":\"5fb28666-fe2b-4a2a-8a9c-1bb244bbf53d\"},{\"pcX\":6,\"moved\":false,\"pcY\":13,\"h\":23,\"i\":\"f42ce4f9-0e56-47da-8220-254436afea6d\",\"orderNum\":25,\"component\":\"JSmoothLine\",\"w\":8,\"x\":6,\"y\":13,\"pageCompId\":\"927733694166769664\",\"key\":\"8b0e7f38-126e-4567-975b-100c6ca49e61\"},{\"pcX\":20,\"moved\":false,\"pcY\":13,\"h\":6,\"i\":\"102fa102-3bd7-4e86-83ea-77cc7ef23651\",\"orderNum\":44,\"component\":\"JText\",\"w\":4,\"x\":20,\"y\":13,\"pageCompId\":\"927733694179352576\",\"key\":\"3d049ce0-e6ae-42c9-8a6b-454bea5f6a81\"},{\"pcX\":17,\"moved\":false,\"pcY\":13,\"h\":6,\"i\":\"201a48c1-924c-4da0-87e5-9dc0eb2579a9\",\"orderNum\":69,\"component\":\"JText\",\"w\":3,\"x\":17,\"y\":13,\"pageCompId\":\"927733694200324096\",\"key\":\"62fe06b3-008a-4fe7-a69a-ef3b6221d8db\"},{\"pcX\":14,\"moved\":false,\"pcY\":19,\"h\":5,\"i\":\"7e2ecc0a-7930-4c76-9c9a-76af22a58f59\",\"orderNum\":78,\"component\":\"JText\",\"w\":10,\"x\":14,\"y\":19,\"pageCompId\":\"927733694217101312\",\"key\":\"8ab5ca31-cf06-490a-87a4-a97678f0d681\"},{\"pcX\":14,\"moved\":false,\"pcY\":13,\"h\":6,\"i\":\"f4029d4e-b19e-4f50-9614-8eb6002b8bef\",\"orderNum\":54,\"component\":\"JText\",\"w\":3,\"x\":14,\"y\":13,\"pageCompId\":\"927733694233878528\",\"key\":\"4ad27d3e-44e5-4ec8-8a2d-f7fc15555f92\"},{\"pcX\":19,\"moved\":false,\"pcY\":24,\"h\":25,\"i\":\"b9fd7a80-7f1f-4ffd-b6dd-3efe1a3c224c\",\"orderNum\":54,\"component\":\"JBar\",\"w\":5,\"x\":19,\"y\":24,\"pageCompId\":\"927733694246461440\",\"key\":\"063a93ea-44a5-4921-9ed4-e741685e28f9\"},{\"pcX\":14,\"moved\":false,\"pcY\":49,\"h\":34,\"i\":\"ec683701-de14-49c5-88d2-684ba2b0983f\",\"orderNum\":54,\"component\":\"JScatter\",\"w\":10,\"x\":14,\"y\":49,\"pageCompId\":\"927733694259044352\",\"key\":\"2c485bcb-6e03-43b2-8f8d-25aee3637bb1\"},{\"pcX\":6,\"moved\":false,\"pcY\":56,\"h\":24,\"i\":\"4aeb9633-f22d-4b40-8ec3-45111146e9fa\",\"orderNum\":54,\"component\":\"JStepLine\",\"w\":8,\"x\":6,\"y\":56,\"pageCompId\":\"927733694267432960\",\"key\":\"6d8562e2-42a4-47e3-83ec-8632e80fd222\"},{\"pcX\":0,\"moved\":false,\"pcY\":20,\"h\":38,\"i\":\"147b58fb-4401-49b9-9db4-017b3c34ca40\",\"orderNum\":59,\"component\":\"JRing\",\"w\":6,\"x\":0,\"y\":20,\"pageCompId\":\"927733694284210176\",\"key\":\"e73c4768-778a-46ab-8748-06713f11cc49\"},{\"pcX\":6,\"moved\":false,\"pcY\":41,\"h\":15,\"i\":\"ab89cda3-73f8-4e04-8259-669cc5411a1c\",\"orderNum\":58,\"component\":\"JProgress\",\"w\":8,\"x\":6,\"y\":41,\"pageCompId\":\"927733694296793088\",\"key\":\"89c3f808-8423-4fd2-b2e3-29563cc00716\"},{\"component\":\"JPie\",\"pcX\":0,\"w\":6,\"moved\":false,\"pcY\":58,\"x\":0,\"h\":22,\"i\":\"5b345a14-4a1a-462d-8653-1bd8555039c1\",\"y\":58,\"orderNum\":58,\"pageCompId\":\"927733694309376000\"}]', 'amVlY2cxMzE0', '1', '1', 'admin', '2024-03-08 15:54:04', 'admin', '2024-03-13 14:05:20', NULL, 1, 69, 7);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733693940277248', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"2023年度消费者权益保护\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":1604,\"height\":78},\"background\":\"#698F38\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"fontSize\":30,\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":500},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733693998997504', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"消费维权信息量\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#22B6D4\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":24},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694024163328', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"5823万\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#22B6D4\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":50},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694045134848', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"网络传播热度指数\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#69AAF5\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":10,\"marginLeft\":24},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694066106368', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"86.23\",\"borderColor\":\"#69AAF5\",\"size\":{\"width\":144,\"height\":56},\"background\":\"#69AAF5\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#FFFFFF\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":13,\"marginLeft\":47},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694087077888', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"头部用户影响力\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":45},\"background\":\"#E8F2DC\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":8},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694099660800', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"消费维权信息走势\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":45},\"background\":\"#E8F2DC\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":5},\"title\":{\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694116438016', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"消费维权数据盘点\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":663,\"height\":45},\"background\":\"#E8F2DC\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":3,\"marginLeft\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694141603840', NULL, '925988379923357696', NULL, 'JPie', '{\"chartData\":\"[  {    \\\"value\\\": 1048,    \\\"name\\\": \\\"男性\\\"  },  {    \\\"value\\\": 735,    \\\"name\\\": \\\"女性\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":326,\"height\":265},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"option\":{\"customColor\":[{\"color\":\"#B8E986\"},{\"color\":\"#7ED321\"}],\"grid\":{\"top\":41,\"left\":49,\"bottom\":115,\"show\":false},\"legend\":{\"r\":1,\"orient\":\"vertical\",\"t\":2,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":false,\"text\":\"基础饼图\",\"textStyle\":{\"color\":\"#464646\"}},\"body\":{\"color\":\"#000000\"},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694166769664', NULL, '925988379923357696', NULL, 'JSmoothLine', '{\"chartData\":\"[  {    \\\"value\\\": 100,    \\\"name\\\": \\\"1月\\\"  },  {    \\\"value\\\": 150,    \\\"name\\\": \\\"2月\\\"  },  {    \\\"value\\\": 60,    \\\"name\\\": \\\"3月\\\"  },  {    \\\"value\\\": 190,    \\\"name\\\": \\\"4月\\\"  },  {    \\\"value\\\": 300,    \\\"name\\\": \\\"5月\\\"  },  {    \\\"value\\\": 200,    \\\"name\\\": \\\"6月\\\"  },  {    \\\"value\\\": 150,    \\\"name\\\": \\\"7月\\\"  },  {    \\\"value\\\": 300,    \\\"name\\\": \\\"8月\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":243},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":33,\"left\":33,\"bottom\":77,\"show\":false,\"right\":25},\"series\":[{\"data\":[],\"type\":\"line\",\"smooth\":true}],\"tooltip\":{\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"subtext\":\"\",\"top\":1,\"left\":1,\"show\":false,\"text\":\"\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694179352576', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"博文阅读量：569万\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":259,\"height\":56},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#417505\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":37},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694200324096', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"互动量：1.68 亿\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#DBEAFF\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"headerBgColor\":\"#FFFFFF\",\"headerColor\":\"#000000\",\"bodyColor\":\"#000000\",\"body\":{\"color\":\"#417505\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":21},\"bodyBgColor\":\"#FFFFFF\",\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694217101312', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"性别—年龄分布\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":663,\"height\":45},\"background\":\"#E8F2DC\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#4A90E2\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":5,\"marginLeft\":8},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694233878528', NULL, '925988379923357696', NULL, 'JText', '{\"chartData\":\"用户数：605万\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":192,\"height\":56},\"background\":\"#E6CDA5\",\"w\":8,\"dataType\":1,\"h\":12,\"turnConfig\":{\"url\":\"\"},\"url\":\"http://api.jeecg.com/mock/42/nav\",\"timeOut\":0,\"option\":{\"body\":{\"color\":\"#417505\",\"text\":\"\",\"fontWeight\":\"bold\",\"marginTop\":11,\"marginLeft\":26},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694246461440', NULL, '925988379923357696', NULL, 'JBar', '{\"chartData\":\"[  {    \\\"name\\\": \\\"70前\\\",    \\\"value\\\": 1879  },  {    \\\"name\\\": \\\"70-80\\\",    \\\"value\\\": 3879  },  {    \\\"name\\\": \\\"80-90\\\",    \\\"value\\\": 2879  },  {    \\\"name\\\": \\\"95后\\\",    \\\"value\\\": 5479  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":326,\"height\":265},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"yAxis\":{\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"interval\":2}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":12,\"left\":38,\"bottom\":81,\"show\":false},\"series\":[{\"barWidth\":40,\"data\":[],\"itemStyle\":{\"color\":\"#64b5f6\",\"borderRadius\":0},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":false,\"text\":\"基础柱形图\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694259044352', NULL, '925988379923357696', NULL, 'JScatter', '{\"chartData\":\"[  {    \\\"name\\\": \\\"1月\\\",    \\\"value\\\": 300  },  {    \\\"name\\\": \\\"2月\\\",    \\\"value\\\": 500  },  {    \\\"name\\\": \\\"3月\\\",    \\\"value\\\": 920  },  {    \\\"name\\\": \\\"4月\\\",    \\\"value\\\": 320  },  {    \\\"name\\\": \\\"5月\\\",    \\\"value\\\": 200  },  {    \\\"name\\\": \\\"6月\\\",    \\\"value\\\": 400  },  {    \\\"name\\\": \\\"7月\\\",    \\\"value\\\": 260  },  {    \\\"name\\\": \\\"8月\\\",    \\\"value\\\": 120  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":663,\"height\":364},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"lineStyle\":{\"color\":\"#f3f3f3\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"},\"interval\":2},\"nameTextStyle\":{\"color\":\"#333333\"}},\"xAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"axisLine\":{\"lineStyle\":{\"color\":\"#333333\"}},\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"symbolSize\":20,\"itemStyle\":{\"color\":\"#64b5f6\"},\"label\":{\"color\":\"#787575\",\"show\":false},\"type\":\"scatter\"}],\"tooltip\":{\"formatter\":\"x:{b}<br/>y:{c}\",\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"show\":true,\"text\":\"消费权益相关话题\",\"textStyle\":{\"color\":\"#464646\"},\"subtextStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694267432960', NULL, '925988379923357696', NULL, 'JStepLine', '{\"chartData\":\"[  {    \\\"value\\\": 10,    \\\"name\\\": \\\"1月\\\"  },  {    \\\"value\\\": 15,    \\\"name\\\": \\\"2月\\\"  },  {    \\\"value\\\": 30,    \\\"name\\\": \\\"3月\\\"  },  {    \\\"value\\\": 29,    \\\"name\\\": \\\"4月\\\"  },  {    \\\"value\\\": 20,    \\\"name\\\": \\\"5月\\\"  },  {    \\\"value\\\": 10,    \\\"name\\\": \\\"6月\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":254},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#909198\"},\"splitLine\":{\"lineStyle\":{\"color\":\"#F3F3F3\"}},\"name\":\"阅读量(亿)\",\"nameTextStyle\":{\"color\":\"#333333\"}},\"grid\":{\"top\":37,\"left\":34,\"bottom\":65,\"show\":false,\"right\":25},\"series\":[{\"data\":[],\"step\":\"middle\",\"type\":\"line\"}],\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":false,\"text\":\"阶梯折线图\",\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694284210176', NULL, '925988379923357696', NULL, 'JRing', '{\"chartData\":\"[  {    \\\"value\\\": 1048,    \\\"name\\\": \\\"视频\\\"  },  {    \\\"value\\\": 735,    \\\"name\\\": \\\"网站\\\"  },  {    \\\"value\\\": 580,    \\\"name\\\": \\\"微信\\\"  },  {    \\\"value\\\": 484,    \\\"name\\\": \\\"微博\\\"  },  {    \\\"value\\\": 300,    \\\"name\\\": \\\"客户端\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":394,\"height\":408},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":0,\"option\":{\"customColor\":[{\"color\":\"#70B324\"},{\"color\":\"#50E3C2\"},{\"color\":\"#F0E032\"},{\"color\":\"#7ED321\"},{\"color\":\"#FFA600\"}],\"grid\":{\"top\":49,\"left\":51,\"bottom\":115,\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"avoidLabelOverlap\":false,\"emphasis\":{\"label\":{\"show\":true,\"fontSize\":\"25\",\"fontWeight\":\"bold\"}},\"label\":{\"color\":\"#000000\",\"show\":true,\"position\":\"center\"},\"labelLine\":{\"show\":false},\"type\":\"pie\",\"radius\":[\"40%\",\"70%\"]}],\"legend\":{\"show\":false},\"tooltip\":{\"trigger\":\"item\",\"textStyle\":{\"color\":\"#464646\"}},\"title\":{\"top\":1,\"left\":1,\"show\":true,\"text\":\"内容来源占比\",\"subtextStyle\":{\"color\":\"#464646\"},\"textStyle\":{\"color\":\"#464646\"}},\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"headColor\":\"#FFFFFF\",\"textStyle\":{\"color\":\"#4A4A4A\"},\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694296793088', NULL, '925988379923357696', NULL, 'JProgress', '{\"chartData\":\"[  {    \\\"name\\\": \\\"阅读量\\\",    \\\"value\\\": 69.86  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":528,\"height\":155},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"option\":{\"yAxis\":{\"axisLabel\":{\"color\":\"#000000\"}},\"grid\":{\"show\":false},\"series\":[{\"barWidth\":19,\"color\":\"#417505\",\"itemStyle\":{\"normal\":{\"barBorderRadius\":10}},\"zlevel\":1,\"label\":{\"formatter\":\"{c}{a}\",\"offset\":[0,-40],\"color\":\"black\",\"show\":true,\"fontSize\":24,\"position\":\"right\"}},{\"barWidth\":19,\"color\":\"#DCF2C2\",\"barGap\":\"-100%\",\"itemStyle\":{\"normal\":{\"barBorderRadius\":10}},\"type\":\"bar\"}],\"tooltip\":{\"axisPointer\":{\"label\":{\"backgroundColor\":\"#333\",\"show\":true},\"type\":\"shadow\"},\"trigger\":\"axis\"},\"title\":{\"show\":false,\"text\":\"基础进度图\",\"textStyle\":{\"color\":\"#464646\"}}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);
INSERT INTO `onl_drag_page_comp` (`id`, `parent_id`, `page_Id`, `comp_id`, `component`, `config`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('927733694309376000', NULL, '925988379923357696', NULL, 'JPie', '{\"chartData\":\"[  {    \\\"value\\\": 1048,    \\\"name\\\": \\\"中性信息\\\"  },  {    \\\"value\\\": 735,    \\\"name\\\": \\\"敏感信息\\\"  },  {    \\\"value\\\": 580,    \\\"name\\\": \\\"非敏感信息\\\"  }]\",\"borderColor\":\"#FFFFFF00\",\"size\":{\"width\":394,\"height\":232},\"dataMapping\":[{\"mapping\":\"\",\"filed\":\"维度\"},{\"mapping\":\"\",\"filed\":\"数值\"}],\"background\":\"#FFFFFF\",\"dataType\":1,\"turnConfig\":{\"url\":\"\"},\"linkageConfig\":[],\"url\":\"http://api.jeecg.com/mock/33/chart\",\"timeOut\":-1,\"option\":{\"customColor\":[{\"color\":\"#417505\"},{\"color\":\"#B8E986\"},{\"color\":\"#F5A623\"}],\"grid\":{\"bottom\":115,\"show\":false},\"legend\":{\"orient\":\"vertical\",\"show\":false},\"series\":[{\"data\":[],\"name\":\"Access From\",\"emphasis\":{\"itemStyle\":{\"shadowOffsetX\":0,\"shadowBlur\":10,\"shadowColor\":\"rgba(0, 0, 0, 0.5)\"}},\"label\":{\"color\":\"#000000\",\"show\":true},\"type\":\"pie\",\"radius\":\"50%\"}],\"isRadius\":false,\"tooltip\":{\"trigger\":\"item\"},\"outRadius\":43,\"title\":{\"subtext\":\"\",\"left\":\"left\",\"show\":true,\"text\":\"敏感信息与非敏感信息占比\",\"textStyle\":{\"color\":\"#464646\"}},\"innerRadius\":87,\"card\":{\"rightHref\":\"\",\"size\":\"default\",\"extra\":\"\",\"title\":\"\"}}}', 'admin', '2024-03-13 11:29:19', NULL, NULL);


ALTER TABLE `sys_user` 
MODIFY COLUMN `birthday` date NULL DEFAULT NULL COMMENT '生日' AFTER `avatar`;

-- 积木报表模板电子收款收据模板
INSERT INTO jimu_report (id, code, name, note, status, type, json_str, api_url, thumb, create_by, create_time, update_by, update_time, del_flag, api_method, api_code, template, view_count, css_str, js_str, py_str, tenant_id) VALUES ('928540173805338624', '20240318105250', '电子收款收据', NULL, NULL, 'printinfo', '{\"loopBlockList\":[],\"printConfig\":{\"layout\":\"portrait\",\"printCallBackUrl\":\"\",\"paper\":\"A4\",\"isBackend\":false,\"width\":210,\"definition\":1,\"marginX\":10,\"height\":297,\"marginY\":10},\"dbexps\":[],\"dicts\":[],\"freeze\":\"A1\",\"dataRectWidth\":686,\"autofilter\":{},\"validations\":[],\"cols\":{\"0\":{\"width\":21},\"1\":{\"width\":35},\"2\":{\"width\":45},\"3\":{\"width\":60},\"4\":{\"width\":128},\"5\":{\"width\":79},\"6\":{\"width\":67},\"7\":{\"width\":67},\"8\":{\"width\":84},\"10\":{\"width\":29},\"len\":100},\"area\":{\"sri\":24,\"sci\":11,\"eri\":24,\"eci\":11,\"width\":100,\"height\":25},\"pyGroupEngine\":false,\"excel_config_id\":\"928540173805338624\",\"hiddenCells\":[],\"zonedEditionList\":[],\"rows\":{\"0\":{\"cells\":{},\"height\":9},\"1\":{\"cells\":{\"2\":{\"rendered\":\"IKbjwc1lQUlSeJfd\",\"display\":\"qrcode\",\"merge\":[2,1],\"style\":0,\"text\":\"http://www.baidu.com\",\"config\":1,\"height\":69},\"3\":{}},\"height\":19},\"2\":{\"cells\":{\"2\":{},\"3\":{},\"4\":{\"merge\":[0,4],\"style\":80,\"text\":\"北京万达物业服务有限公司\",\"height\":25},\"9\":{\"virtual\":\"YfdiQDcuqTjlOG4f\",\"style\":67,\"text\":\" \"}}},\"3\":{\"cells\":{\"2\":{},\"3\":{},\"4\":{\"merge\":[0,4],\"style\":72,\"text\":\"电子收款收据\",\"height\":25}}},\"4\":{\"cells\":{\"2\":{},\"3\":{}},\"height\":16},\"5\":{\"cells\":{\"1\":{\"rendered\":\"\",\"merge\":[0,2],\"style\":41,\"text\":\"客户/业主\",\"config\":\"\",\"height\":25},\"4\":{\"merge\":[0,1],\"style\":43,\"text\":\"#{receipt.customName}\",\"height\":25},\"5\":{},\"6\":{\"merge\":[0,1],\"style\":44,\"text\":\"收款单号\",\"height\":25},\"8\":{\"merge\":[0,1],\"style\":45,\"text\":\"#{receipt.receiptNo}\",\"height\":25},\"9\":{}},\"height\":25},\"6\":{\"cells\":{\"1\":{\"rendered\":\"\",\"merge\":[0,2],\"style\":44,\"text\":\"实际交款人\",\"config\":\"\",\"height\":25},\"4\":{\"merge\":[0,1],\"style\":43,\"text\":\"#{receipt.actualPayer}\",\"height\":25},\"5\":{},\"6\":{\"rendered\":\"\",\"merge\":[0,1],\"style\":44,\"text\":\"结算方式\",\"config\":\"\",\"height\":25},\"8\":{\"merge\":[0,1],\"style\":45,\"text\":\"#{receipt.settlementType}\",\"height\":25},\"9\":{}},\"height\":25},\"7\":{\"cells\":{\"1\":{\"merge\":[0,2],\"style\":44,\"text\":\"收款时间\",\"height\":25},\"4\":{\"merge\":[0,1],\"style\":43,\"text\":\"#{receipt.collectionTime}\",\"height\":25},\"5\":{},\"6\":{\"merge\":[0,1],\"style\":44,\"text\":\"原单号\",\"height\":25},\"8\":{\"merge\":[0,1],\"style\":45,\"text\":\"#{receipt.originalNumber}\",\"height\":25},\"9\":{}},\"height\":25},\"8\":{\"cells\":{\"1\":{\"merge\":[0,2],\"style\":44,\"text\":\"收款组织\",\"height\":25},\"4\":{\"merge\":[0,5],\"style\":46,\"text\":\"#{receipt.organization}\",\"height\":25},\"5\":{},\"6\":{},\"7\":{},\"8\":{},\"9\":{},\"11\":{\"rendered\":\"\",\"text\":\"\",\"config\":\"\"}},\"height\":25},\"9\":{\"cells\":{\"1\":{}}},\"10\":{\"cells\":{\"1\":{\"merge\":[0,1],\"style\":19,\"text\":\"行号\",\"height\":38},\"3\":{\"merge\":[0,1],\"style\":47,\"text\":\"房产名称\",\"height\":38},\"5\":{\"rendered\":\"\",\"merge\":[0,2],\"style\":47,\"text\":\"费用项目\",\"config\":\"\",\"height\":38},\"8\":{\"style\":47,\"text\":\"应交月份\"},\"9\":{\"style\":19,\"text\":\"金额\"}},\"height\":38},\"11\":{\"cells\":{\"1\":{\"merge\":[0,1],\"text\":\"=row()\",\"height\":34},\"3\":{\"merge\":[0,1],\"text\":\"#{receiptProject.propertyName}\",\"height\":34},\"5\":{\"merge\":[0,2],\"text\":\"#{receiptProject.expenseItem}\",\"height\":34},\"8\":{\"style\":0,\"text\":\"#{receiptProject.payableMonths}\"},\"9\":{\"style\":83,\"text\":\"#{receiptProject.price}\"},\"13\":{\"rendered\":\"\",\"text\":\"\",\"config\":\"\"}},\"height\":34},\"12\":{\"cells\":{\"1\":{\"merge\":[0,2],\"style\":17,\"text\":\"款项合计\",\"height\":30},\"4\":{\"merge\":[0,5],\"style\":82,\"text\":\"人民币 =sum(J12) 元\",\"height\":30},\"11\":{\"rendered\":\"\",\"text\":\"\",\"config\":\"\"},\"15\":{\"rendered\":\"\",\"text\":\"\",\"config\":\"\"}},\"height\":30},\"13\":{\"cells\":{\"1\":{\"merge\":[0,2],\"style\":19,\"text\":\"缴费说明\",\"height\":30},\"4\":{\"merge\":[0,5],\"style\":23,\"text\":\"#{receipt.paymentDesc}\",\"height\":30},\"15\":{\"rendered\":\"\",\"text\":\"\",\"config\":\"\"}},\"height\":30},\"14\":{\"cells\":{\"1\":{\"merge\":[0,2],\"style\":19,\"text\":\"检验密码\",\"height\":35},\"4\":{\"merge\":[0,5],\"style\":84,\"text\":\"#{receipt.verifyPassword}\",\"height\":35},\"5\":{},\"6\":{},\"7\":{},\"8\":{},\"9\":{}},\"height\":35},\"15\":{\"cells\":{\"8\":{\"text\":\" \",\"virtual\":\"qWMWhe42wLnQXDUa\"}},\"height\":10},\"16\":{\"cells\":{\"1\":{\"merge\":[0,2],\"style\":19,\"text\":\"收款经办\",\"height\":30},\"4\":{\"style\":20,\"text\":\"#{receipt.dealWith}\"},\"5\":{\"merge\":[0,1],\"style\":19,\"text\":\"公司签章\",\"height\":30},\"7\":{\"merge\":[0,2],\"style\":23,\"text\":\" \",\"height\":30}},\"height\":30},\"17\":{\"cells\":{\"8\":{\"rendered\":\"\",\"text\":\"\",\"config\":\"\"}},\"height\":10},\"18\":{\"cells\":{\"1\":{\"merge\":[0,1],\"style\":29,\"text\":\"加密说明：\",\"height\":25}}},\"19\":{\"cells\":{\"1\":{\"merge\":[0,8],\"style\":63,\"text\":\"1、本电子收据加密信息算法使用 SHA256\",\"height\":30},\"2\":{},\"3\":{},\"4\":{},\"5\":{},\"6\":{},\"7\":{},\"8\":{},\"9\":{}},\"height\":30},\"20\":{\"cells\":{\"1\":{\"merge\":[0,8],\"style\":65,\"text\":\"2、原始输入信息依次为:法人名称、收款组织、客户/业主、实际交款人、结算方式、收款单号、收款时间、款项合计,使用RSA私钥加密信息摘要生成“校验密码”\",\"height\":37},\"2\":{},\"3\":{},\"4\":{},\"5\":{},\"6\":{},\"7\":{},\"8\":{},\"9\":{}},\"height\":37},\"21\":{\"cells\":{\"1\":{\"merge\":[0,8],\"style\":65,\"text\":\"3、应公钥为MIGEMAOCCSqGSIb3DQEBAQUAMGNADCBiQKBgQCEf iYMBHNInswq1N/KLBy smaG68D+nlQk/RZ3QVSTmWo310WnSipYR3ksCNBksyGZceEneCIEuWBboPImYQUbH/ EHP3I3Ri 1E1 INjNI3GvQ7oXH/RLmHRGAgCz 4d9QcW7mGTHVvj/3o/gEvqyHgW6eG1lsl f6aEi7mi IRHxYtZrFQIDAQAB\",\"height\":57},\"2\":{},\"3\":{},\"4\":{},\"5\":{},\"6\":{},\"7\":{},\"8\":{},\"9\":{}},\"height\":57},\"23\":{\"cells\":{},\"height\":25},\"len\":200},\"rpbar\":{\"show\":true,\"pageSize\":\"\",\"btnList\":[]},\"fixedPrintHeadRows\":[],\"fixedPrintTailRows\":[],\"displayConfig\":{\"1\":{\"colorDark\":\"#000000\",\"width\":69,\"text\":\"http://www.baidu.com\",\"colorLight\":\"#ffffff\",\"height\":69}},\"background\":false,\"name\":\"sheet1\",\"styles\":[{\"align\":\"center\"},{\"font\":{\"size\":7.5}},{\"font\":{\"size\":8}},{\"align\":\"center\",\"font\":{\"size\":12}},{\"font\":{\"size\":12}},{\"align\":\"center\",\"font\":{\"size\":12,\"bold\":true}},{\"font\":{\"size\":12,\"bold\":true}},{\"align\":\"center\",\"font\":{\"size\":11}},{\"font\":{\"size\":11}},{\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"font\":{\"size\":11,\"bold\":true}},{\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"bgcolor\":\"#dae7d2\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#000\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"],\"right\":[\"thin\",\"#1e8a1e\"]}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"]}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]}},{\"bgcolor\":\"#dae7d2\",\"align\":\"center\"},{\"bgcolor\":\"#dae7d2\"},{\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"bold\":true}},{\"bgcolor\":\"#dae7d2\",\"font\":{\"bold\":true}},{\"align\":\"center\",\"font\":{\"bold\":true}},{\"font\":{\"bold\":true}},{\"bgcolor\":\"【QQYUN-8110】在线通讯录支持设置权限\",\"align\":\"center\",\"font\":{\"bold\":true}},{\"bgcolor\":\"【QQYUN-8110】在线通讯录支持设置权限\",\"font\":{\"bold\":true}},{\"color\":\"【QQYUN-8110】在线通讯录支持设置权限\"},{\"color\":\"#0a0a0a\"},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"bold\":true}},{\"border\":{\"left\":[\"thin\",\"#1e8a1e\"]}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"left\":[\"thin\",\"#1e8a1e\"]}},{\"border\":{\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":10.5,\"bold\":true}},{\"bgcolor\":\"#dae7d2\",\"font\":{\"size\":10.5,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"valign\":\"middle\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"bgcolor\":\"#dae7d2\",\"valign\":\"middle\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"],\"right\":[\"thin\",\"#1e8a1e\"]},\"valign\":\"middle\"},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"valign\":\"middle\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"valign\":\"middle\"},{\"border\":{\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"valign\":\"middle\"},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"],\"right\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{\"bgcolor\":\"sk-JsZB4Wi1HM2zJne40434CbCd071642Ab9910Ee1cA4CaE115\"},{\"bgcolor\":\"#dae7d2\",\"font\":{\"size\":11}},{\"border\":{\"top\":[\"thin\",\"#000\"]},\"bgcolor\":\"#dae7d2\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#000\"]},\"bgcolor\":\"#dae7d2\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#000\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"font\":{\"size\":10}},{\"font\":{\"size\":10}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"font\":{\"size\":11}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"font\":{\"size\":11,\"bold\":true}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"align\":\"center\",\"font\":{\"size\":11,\"bold\":true}},{},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"align\":\"center\"},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\"},{\"border\":{\"bottom\":[\"thin\",\"#1e8a1e\"]}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"bgcolor\":\"#dae7d2\",\"align\":\"center\",\"font\":{\"size\":11}},{\"valign\":\"bottom\"},{\"textwrap\":true},{\"textwrap\":true,\"valign\":\"top\"},{\"align\":\"center\",\"font\":{\"size\":14}},{\"font\":{\"size\":14}},{\"font\":{\"size\":14,\"bold\":true}},{\"align\":\"center\",\"font\":{\"size\":14,\"bold\":true}},{\"align\":\"center\",\"font\":{\"size\":15,\"bold\":true}},{\"font\":{\"size\":15}},{\"align\":\"center\",\"font\":{\"size\":15,\"name\":\"宋体\",\"bold\":true}},{\"font\":{\"size\":15,\"name\":\"宋体\"}},{\"align\":\"center\",\"font\":{\"size\":15,\"name\":\"楷体\",\"bold\":true}},{\"font\":{\"size\":15,\"name\":\"楷体\"}},{\"align\":\"center\",\"font\":{\"size\":15,\"name\":\"仿宋\",\"bold\":true}},{\"font\":{\"size\":15,\"name\":\"仿宋\"}},{\"align\":\"center\",\"font\":{\"size\":15,\"name\":\"华文行楷\",\"bold\":true}},{\"font\":{\"size\":15,\"name\":\"华文行楷\"}},{\"align\":\"center\",\"font\":{\"size\":14,\"name\":\"宋体\"}},{\"font\":{\"name\":\"宋体\"}},{\"border\":{\"top\":[\"thin\",\"#1e8a1e\"],\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"font\":{\"bold\":true}},{\"align\":\"right\"},{\"border\":{\"left\":[\"thin\",\"#1e8a1e\"],\"bottom\":[\"thin\",\"#1e8a1e\"]},\"textwrap\":true}],\"merges\":[\"C2:D4\",\"E3:I3\",\"E4:I4\",\"B6:D6\",\"E6:F6\",\"G6:H6\",\"I6:J6\",\"B7:D7\",\"E7:F7\",\"G7:H7\",\"I7:J7\",\"B8:D8\",\"E8:F8\",\"G8:H8\",\"I8:J8\",\"B9:D9\",\"E9:J9\",\"B11:C11\",\"D11:E11\",\"F11:H11\",\"B12:C12\",\"D12:E12\",\"F12:H12\",\"B13:D13\",\"E13:J13\",\"B14:D14\",\"E14:J14\",\"B15:D15\",\"E15:J15\",\"B17:D17\",\"F17:G17\",\"H17:J17\",\"B19:C19\",\"B20:J20\",\"B21:J21\",\"B22:J22\"],\"imgList\":[{\"row\":2,\"col\":9,\"colspan\":1,\"rowspan\":2,\"width\":\"96\",\"height\":\"47\",\"src\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/jimureport/images/wanda_1710733731369.jpg\",\"layer_id\":\"YfdiQDcuqTjlOG4f\",\"offsetX\":0,\"offsetY\":0,\"virtualCellRange\":[[2,9]]},{\"row\":15,\"col\":8,\"colspan\":1,\"rowspan\":5,\"width\":\"82\",\"height\":\"77\",\"src\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/jimureport/images/dzyz_1710741011435.png\",\"layer_id\":\"qWMWhe42wLnQXDUa\",\"offsetX\":0,\"offsetY\":0,\"virtualCellRange\":[[15,8]]}]}', NULL, NULL, 'admin', '2024-03-18 10:52:51', 'admin', '2024-03-20 10:16:40', 0, NULL, NULL, 1, 57, NULL, NULL, NULL, '1');
INSERT INTO jimu_report_db (id, jimu_report_id, create_by, update_by, create_time, update_time, db_code, db_ch_name, db_type, db_table_name, db_dyn_sql, db_key, tb_db_key, tb_db_table_name, java_type, java_value, api_url, api_method, is_list, is_page, db_source, db_source_type, json_data, api_convert) VALUES ('930045149249585152', '928540173805338624', 'admin', 'admin', '2024-03-20 10:18:16', '2024-03-20 10:18:16', 'receipt', '顾客/业主收费明细', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'http://api.jeecg.com/mock/26/receipt?id=${id}', '0', 0, '0', '', NULL, '', '');
INSERT INTO jimu_report_db (id, jimu_report_id, create_by, update_by, create_time, update_time, db_code, db_ch_name, db_type, db_table_name, db_dyn_sql, db_key, tb_db_key, tb_db_table_name, java_type, java_value, api_url, api_method, is_list, is_page, db_source, db_source_type, json_data, api_convert) VALUES ('930241691461926912', '928540173805338624', 'admin', 'admin', '2024-03-20 10:07:37', '2024-03-20 10:07:37', 'receiptProject', '顾客/业主收费项目明细', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'http://api.jeecg.com/mock/26/receiptProject?customId=${customId}', '0', 1, '0', '', NULL, '', '');
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930045149299916800', 'admin', '2024-03-19 20:34:13', NULL, NULL, '930045149249585152', 'id', '编号(输入1或者2)', 'String', NULL, 0, 1, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930045149438328832', 'admin', '2024-03-19 20:34:13', NULL, NULL, '930045149249585152', 'organization', '收款组织', 'String', NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634357579776', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'customName', '顾客名称', 'String', NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634374356992', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'receiptNo', '收款单号', 'String', NULL, 3, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634386939904', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'actualPayer', '实际交款人', 'String', NULL, 4, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634403717120', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'settlementType', '结算方式', 'String', NULL, 5, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634420494336', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'collectionTime', '收款时间', 'String', NULL, 6, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634433077248', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'originalNumber', '原单号', 'String', NULL, 7, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634449854464', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'paymentDesc', '缴费说明', 'String', NULL, 8, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634462437376', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'verifyPassword', '检验密码', 'String', NULL, 9, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930244634479214592', 'admin', '2024-03-20 09:46:54', NULL, NULL, '930045149249585152', 'dealWith', '收款经办', 'String', NULL, 10, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930241691537424384', 'admin', '2024-03-20 09:35:13', NULL, NULL, '930241691461926912', 'id', '编号', 'String', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930241691583561728', 'admin', '2024-03-20 09:35:13', NULL, NULL, '930241691461926912', 'customId', '顾客/业主收费明细编号', 'String', NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930241691600338944', 'admin', '2024-03-20 09:35:13', NULL, NULL, '930241691461926912', 'propertyName', '房产名称', 'String', NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930241691617116160', 'admin', '2024-03-20 09:35:13', NULL, NULL, '930241691461926912', 'expenseItem', '费用项目', 'String', NULL, 3, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930241691650670592', 'admin', '2024-03-20 09:35:13', NULL, NULL, '930241691461926912', 'price', '应交月份', 'String', NULL, 4, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_field (id, create_by, create_time, update_by, update_time, jimu_report_db_id, field_name, field_text, widget_type, widget_width, order_num, search_flag, search_mode, dict_code, search_value, search_format, ext_json) VALUES ('930243856901390336', 'admin', '2024-03-20 09:43:49', NULL, NULL, '930241691461926912', 'payableMonths', '金额', 'String', NULL, 5, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO jimu_report_db_param (id, jimu_report_head_id, param_name, param_txt, param_value, order_num, create_by, create_time, update_by, update_time, search_flag, widget_type, search_mode, dict_code, search_format, ext_json) VALUES ('930241749263630336', '930045149249585152', 'id', NULL, '', 1, 'admin', '2024-03-20 09:35:26', NULL, NULL, 0, NULL, NULL, NULL, NULL, '');
INSERT INTO jimu_report_db_param (id, jimu_report_head_id, param_name, param_txt, param_value, order_num, create_by, create_time, update_by, update_time, search_flag, widget_type, search_mode, dict_code, search_format, ext_json) VALUES ('930241691675836416', '930241691461926912', 'customId', NULL, '1', 1, 'admin', '2024-03-20 09:35:13', NULL, NULL, 0, NULL, NULL, NULL, NULL, '');
INSERT INTO jimu_report_link (id, report_id, parameter, eject_type, link_name, api_method, link_type, api_url, link_chart_id, expression, requirement) VALUES ('929546942631428096', '928540173805338624', '{\"main\":\"receipt\",\"sub\":\"receiptProject\",\"subReport\":[{\"mainField\":\"id\",\"subParam\":\"customId\",\"tableIndex\":1}]}', NULL, '收款明细', NULL, '4', NULL, NULL, NULL, NULL);

-- 仪表盘新增当前时间、卡片组件
INSERT INTO `onl_drag_comp`(`id`, `parent_id`, `comp_name`, `comp_type`, `icon`, `order_num`, `type_id`, `comp_config`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('100104108', '100104', '简洁卡片', 'JSimpleCard', 'mdi:card-bulleted-outline', 8, NULL, '{\n  \"w\": 24,\n  \"h\": 14,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"title\": \"访问数\",\n      \"icon\": \"icon-jeecg-qianbao\",\n      \"value\": 2000,\n      \"color\": \"green\",\n      \"suffix\": \"月\"\n    },\n    {\n      \"title\": \"成交额\",\n      \"icon\": \"icon-jeecg-youhuiquan\",\n      \"value\": 20000,\n      \"color\": \"blue\",\n      \"suffix\": \"月\"\n    },\n    {\n      \"title\": \"下载数\",\n      \"icon\": \"icon-jeecg-tupian\",\n      \"value\": 8000,\n      \"color\": \"orange\",\n      \"suffix\": \"周\"\n    },\n    {\n      \"title\": \"成交数\",\n      \"icon\": \"icon-jeecg-jifen\",\n      \"value\": 5000,\n      \"color\": \"purple\",\n      \"suffix\": \"年\"\n    }\n  ],\n  \"option\": {\n    \"icon\": {\n      \"scriptUrl\": \"//at.alicdn.com/t/font_3237315_b3fqd960glt.js\",\n      \"fontSize\": 20\n    },\n    \"card\": {\n      \"title\": \"卡片\",\n      \"extra\": \"更多\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"body\": {\n      \"horizontal\": 8,\n      \"vertical\": 8,\n      \"span\": 6\n    },\r\n		\"icon\": {\r\n		 \"fontSize\": 50\r\n		}\n  }\n}', '1', NULL, NULL, 'admin', '2022-05-07 18:24:23');
INSERT INTO `onl_drag_comp`(`id`, `parent_id`, `comp_name`, `comp_type`, `icon`, `order_num`, `type_id`, `comp_config`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('932219134883299328', '100', '当前时间', 'JCurrentTime', 'ant-design:field-time-outlined', 100, NULL, '{\n  \"w\": 5,\n  \"h\": 4,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\n  \"timeOut\": 0,\n	\"background\": \"#4A90E2\",\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"chartData\": \"\",\n  \"option\": {\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"body\": {\n      \"text\": \"\",\n      \"color\": \"#FFFFFF\",\n      \"fontWeight\": \"bold\",\n      \"marginLeft\": 0,\n      \"marginTop\": 0\n    }\n  }\n}', '1', 'jeecg', '2024-03-25 20:32:51', 'jeecg', '2024-03-25 20:34:14');
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '文本', `comp_type` = 'JText', `icon` = 'ant-design:font-colors-outlined', `order_num` = 14, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 5,\r\n  \"h\": 4,\r\n  \"dataType\": 1,\r\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\r\n  \"timeOut\": 0,\r\n	\"background\": \"#4A90E2\",\r\n	\"linkageConfig\": [],\r\n	\"dataMapping\": [\r\n    {\r\n      \"filed\": \"数值\",\r\n      \"mapping\": \"\"\r\n    }\r\n  ],\r\n  \"turnConfig\": {\r\n    \"url\": \"\"\r\n  },\r\n  \"chartData\": \"JeecgBoot面板设计\",\r\n  \"option\": {\r\n    \"card\": {\r\n      \"title\": \"\",\r\n      \"extra\": \"\",\r\n      \"rightHref\": \"\",\r\n      \"size\": \"default\"\r\n    },\r\n    \"body\": {\r\n      \"text\": \"\",\r\n      \"color\": \"#FFFFFF\",\r\n      \"fontWeight\": \"bold\",\r\n      \"marginLeft\": 0,\r\n      \"marginTop\": 0\r\n    }\r\n  }\r\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 10:49:04' WHERE `id` = '100110';

-- 【issues/6017】 添加对接钉钉部门的id
ALTER TABLE sys_depart 
ADD COLUMN ding_identifier varchar(100) NULL COMMENT '对接钉钉部门的ID' AFTER qywx_identifier;

-- 修改我的租户子菜单下的组件名称
UPDATE sys_permission SET component_name = 'tenant-system-user' WHERE id = '119213522910765570';
UPDATE sys_permission SET component_name = 'tenant-role-list' WHERE id = '1597419994965786625';
UPDATE sys_permission SET component_name = 'tenant-my-tenant-list' WHERE id = '1663816667704500225';

-- 添加"异常"日志类型字典
INSERT INTO `sys_dict_item`(`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`)
VALUES ('1782325511230337025', '83bfb33147013cc81640d5fd9eda030c', '异常日志', '3', NULL, 1, 1, 'admin', '2024-04-22 16:28:11', NULL, NULL, NULL);

-- 添加"异常"和"租户操作"日志类型字典
-- 将原本异常日志修改为租户操作日志
UPDATE `sys_dict_item` SET `item_text` = '租户操作日志'  WHERE `id` = '1782325511230337025';
-- 添加新的异常日志类型
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`)
VALUES ('1783383857978970114', '83bfb33147013cc81640d5fd9eda030c', '异常日志', '4', NULL, 3, 1, 'jeecg', '2024-04-25 14:33:40', NULL, NULL, NULL);

-- 字段类型问题---
ALTER TABLE `sys_permission` 
MODIFY COLUMN `hidden` tinyint NULL DEFAULT 0 COMMENT '是否隐藏路由: 0否,1是' AFTER `keep_alive`,
MODIFY COLUMN `hide_tab` tinyint NULL DEFAULT NULL COMMENT '是否隐藏tab: 0否,1是' AFTER `hidden`;

-- 添加原生总结栏菜单---
INSERT INTO `sys_permission` (`id`,`parent_id`,`name`,`url`,`component`,`is_route`,`component_name`,`redirect`,`menu_type`,`perms`,`perms_type`,`sort_no`,`always_show`,`icon`,`is_leaf`,`keep_alive`,`hidden`,`hide_tab`,`description`,`create_by`,`create_time`,`update_by`,`update_time`,`del_flag`,`rule_flag`,`status`,`internal_or_external`)
VALUES ('1783334031927627777','1438108198113501186','原生总结栏','/comp/table/tableSummary','demo/table/AntdTableSummary',1,'',NULL,1,NULL,'0',1.00,0,NULL,1,0,0,0,NULL,'admin','2024-04-25 11:15:40',NULL,NULL,0,0,NULL,0);

-- 修改日志表字段长度---
ALTER TABLE `sys_log` 
MODIFY COLUMN `log_content` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '日志内容' AFTER `log_type`,
MODIFY COLUMN `method` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求java方法' AFTER `ip`;

-- 日志表加字段，用于区分请求来源的客户端---
ALTER TABLE `sys_log` 
ADD COLUMN `client_type` varchar(5) NULL COMMENT '客户端类型 pc:电脑端 app:手机端 h5:移动网页端' AFTER `tenant_id`;

INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`, `low_app_id`) VALUES ('1784843187992084482', '客户终端类型', 'client_type', NULL, 0, 'jeecg', '2024-04-29 15:12:31', NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1784843259509161986', '1784843187992084482', '电脑终端', 'pc', NULL, NULL, 1, 1, 'jeecg', '2024-04-29 15:12:49', NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1784843314429378562', '1784843187992084482', '手机APP端', 'app', NULL, NULL, 1, 1, 'jeecg', '2024-04-29 15:13:02', NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('1784843380502249474', '1784843187992084482', '移动网页端', 'h5', NULL, NULL, 1, 1, 'jeecg', '2024-04-29 15:13:17', NULL, NULL);


-- 修改面积图的默认配色
UPDATE `onl_drag_comp` SET `parent_id` = '200', `comp_name` = '面积图', `comp_type` = 'JArea', `icon` = 'teenyicons:area-chart-solid', `order_num` = 6, `type_id` = NULL, `comp_config` = '{\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": -1,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1048,\n      \"name\": \"华为\"\n    },\n    {\n      \"value\": 605,\n      \"name\": \"vivo\"\n    },\n    {\n      \"value\": 580,\n      \"name\": \"oppo\"\n    },\n    {\n      \"value\": 484,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 300,\n      \"name\": \"小米\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"基础面积图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"xAxis\": {\n      \"boundaryGap\": false,\n      \"data\": []\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"type\": \"line\",\n        \"areaStyle\": {},\r\n				\"itemStyle\": {\r\n          \"color\": \"#64b5f6\"\r\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-22 19:04:01' WHERE `id` = '200206';
-- 新增区域地图
INSERT INTO `onl_drag_comp`(`id`, `parent_id`, `comp_name`, `comp_type`, `icon`, `order_num`, `type_id`, `comp_config`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('100120107', '100120', '区域地图', 'JAreaMap', 'ic:outline-scatter-plot', 1, NULL, '{\n  \"w\": 12,\n  \"h\": 30,\n  \"activeKey\": 1,\n  \"dataType\": 1,\n  \"background\": \"#ffffff\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n       \"dataMapping\": [{\n        \"filed\": \"区域\",\n        \"mapping\": \"\"\n    }, {\n        \"filed\": \"数值\",\n        \"mapping\": \"\"\n    }],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"jsConfig\": \"\",\n  \"chartData\": [\n    {\n      \"name\": \"北京\",\n      \"value\": 199\n    },\n    {\n      \"name\": \"天津\",\n      \"value\": 42\n    },\n    {\n      \"name\": \"河北\",\n      \"value\": 102\n    },\n    {\n      \"name\": \"山西\",\n      \"value\": 81\n    },\n    {\n      \"name\": \"内蒙古\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"辽宁\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"吉林\",\n      \"value\": 82\n    },\n    {\n      \"name\": \"黑龙江\",\n      \"value\": 123\n    },\n    {\n      \"name\": \"上海\",\n      \"value\": 24\n    },\n    {\n      \"name\": \"江苏\",\n      \"value\": 92\n    },\n    {\n      \"name\": \"浙江\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"安徽\",\n      \"value\": 109\n    },\n    {\n      \"name\": \"福建\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"江西\",\n      \"value\": 91\n    },\n    {\n      \"name\": \"山东\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"河南\",\n      \"value\": 137\n    },\n    {\n      \"name\": \"湖北\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"湖南\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"重庆\",\n      \"value\": 91\n    },\n    {\n      \"name\": \"四川\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"贵州\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"云南\",\n      \"value\": 83\n    },\n    {\n      \"name\": \"西藏\",\n      \"value\": 9\n    },\n    {\n      \"name\": \"陕西\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"甘肃\",\n      \"value\": 56\n    },\n    {\n      \"name\": \"青海\",\n      \"value\": 10\n    },\n    {\n      \"name\": \"宁夏\",\n      \"value\": 18\n    },\n    {\n      \"name\": \"新疆\",\n      \"value\": 180\n    },\n    {\n      \"name\": \"广东\",\n      \"value\": 123\n    },\n    {\n      \"name\": \"广西\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"海南\",\n      \"value\": 14\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#fff176\",\n    \"barColor2\": \"#fcc02e\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#fcc02e\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#000000\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"left\": 10,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 200,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        0\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": true,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#fff59c\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', '1', NULL, NULL, 'admin', '2022-08-05 10:32:09');
-- 新增南丁格尔玫瑰图
INSERT INTO `onl_drag_comp`(`id`, `parent_id`, `comp_name`, `comp_type`, `icon`, `order_num`, `type_id`, `comp_config`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('200203', '200', '南丁格尔玫瑰图', 'JRose', 'ant-design:pie-chart-outlined', 2, NULL, '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": -1,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1048,\n      \"name\": \"vivo\"\n    },\n    {\n      \"value\": 735,\n      \"name\": \"oppo\"\n    },\n    {\n      \"value\": 580,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 484,\n      \"name\": \"小米\"\n    },\n    {\n      \"value\": 300,\n      \"name\": \"三星\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"title\": {\n      \"text\": \"基础饼图\",\n      \"subtext\": \"\",\n      \"left\": \"left\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"item\"\n    },\n    \"legend\": {\n      \"orient\": \"vertical\"\n    },\n    \"series\": [\n      {\n        \"name\": \"南丁格尔玫瑰\",\n        \"type\": \"pie\",\n        \"radius\": \"50%\",\r\n        \"roseType\": \"radius\",\n        \"data\": [],\n        \"emphasis\": {\n          \"itemStyle\": {\n            \"shadowBlur\": 10,\n            \"shadowOffsetX\": 0,\n            \"shadowColor\": \"rgba(0, 0, 0, 0.5)\"\n          }\n        }\n      }\n    ]\n  }\n}', '1', NULL, NULL, 'admin', '2022-05-23 14:07:29');
-- -新增条形图
INSERT INTO `onl_drag_comp`(`id`, `parent_id`, `comp_name`, `comp_type`, `icon`, `order_num`, `type_id`, `comp_config`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('200200208', '200200', '基础条形图', 'JHorizontalBar', 'ic:baseline-bar-chart', NULL, NULL, '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"苹果\",\n      \"value\": 1000879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"三星\",\n      \"value\": 3400879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"小米\",\n      \"value\": 2300879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"oppo\",\n      \"value\": 5400879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"vivo\",\n      \"value\": 3400879,\n      \"type\": \"手机品牌\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"top\":90, \n      \"bottom\": 115,\r\n			\"containLabel\": true\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"基础条形图\",\n      \"show\": true,\n      \"textStyle\": {\n        \"color\": \"#464646\",\r\n				\"fontWeight\":\"normal\"\n      }\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"yAxis\": {\r\n		  \"type\": \"category\",\n      \"splitLine\": {\n        \"interval\": 2\n      },\n      \"lineStyle\": {\n        \"color\": \"#f3f3f3\"\n      }\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"type\": \"bar\",\n        \"barWidth\": 40,\n        \"itemStyle\": {\n          \"color\": \"#64b5f6\",\n          \"borderRadius\": 0\n        }\n      }\n    ]\n  }\n}', '1', NULL, NULL, 'admin', '2022-08-04 19:18:21');


-- online修改索引会导致同步失败，提示索引已经存在 ---
ALTER TABLE `onl_cgform_index`
    ADD COLUMN `index_name_old` varchar(100) NULL COMMENT '原索引名称' AFTER `index_name`;
    
-- 加图标js增强保存失败 ---
ALTER TABLE `onl_cgform_button`
MODIFY COLUMN `BUTTON_ICON` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '按钮图标';

-- 替换api接口地址为https开头--
UPDATE jimu_report_db SET api_url = REPLACE(api_url, 'http://api.jeecg.com', 'https://api.jeecg.com')
WHERE api_url LIKE 'http://api.jeecg.com%';

-- 修改当前时间组件配置项
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '当前时间', `comp_type` = 'JCurrentTime', `icon` = 'ant-design:field-time-outlined', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\n  \"w\": 5,\n  \"h\": 4,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\n  \"timeOut\": 0,\n	\"background\": \"#4A90E2\",\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"chartData\": \"\",\n  \"option\": {\r\n	  \"showWeek\":\"show\",\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"body\": {\n      \"text\": \"\",\n      \"color\": \"#FFFFFF\",\n      \"fontWeight\": \"lighter\",\n      \"marginLeft\": 0,\n      \"marginTop\": 0\n    }\n  }\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2024-03-25 20:32:51', `update_by` = 'jeecg', `update_time` = '2024-03-25 20:34:14' WHERE `id` = '932219134883299328';

-- 修改当前时间组件/环形图配置项
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '当前时间', `comp_type` = 'JCurrentTime', `icon` = 'ant-design:field-time-outlined', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\n  \"w\": 5,\n  \"h\": 6,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\n  \"timeOut\": 0,\n	\"background\": \"#3F7DD4\",\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"chartData\": \"\",\n  \"option\": {\r\n	  \"showWeek\":\"show\",\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"body\": {\n      \"text\": \"\",\n      \"color\": \"#FFFFFF\",\n      \"fontWeight\": \"normal\",\n      \"marginLeft\": 0,\n      \"marginTop\": 13\n    }\n  }\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2024-03-25 20:32:51', `update_by` = 'jeecg', `update_time` = '2024-03-25 20:34:14' WHERE `id` = '932219134883299328';
UPDATE `onl_drag_comp` SET `parent_id` = '200', `comp_name` = '环形图', `comp_type` = 'JRing', `icon` = 'mdi:chart-donut', `order_num` = 5, `type_id` = NULL, `comp_config` = '{\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1048,\n      \"name\": \"oppo\"\n    },\n    {\n      \"value\": 735,\n      \"name\": \"vivo\"\n    },\n    {\n      \"value\": 580,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 484,\n      \"name\": \"小米\"\n    },\n    {\n      \"value\": 300,\n      \"name\": \"三星\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"title\": {\n      \"text\": \"基础环形图\",\n      \"show\": true\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"item\"\n    },\n    \"series\": [\n      {\n        \"name\": \"Access From\",\n        \"type\": \"pie\",\n        \"radius\": [\n          \"40%\",\n          \"70%\"\n        ],\n        \"avoidLabelOverlap\": false,\n        \"label\": {\n          \"show\": false,\n          \"position\": \"center\"\n        },\n        \"emphasis\": {\n          \"label\": {\n            \"show\": true,\n            \"fontWeight\": \"bold\",\r\n						\"fontSize\": 14\n          }\n        },\n        \"labelLine\": {\n          \"show\": false\n        },\n        \"data\": []\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-22 19:05:11' WHERE `id` = '200205';

-- 仪表盘组件配置项修改
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '文本', `comp_type` = 'JText', `icon` = 'ant-design:font-colors-outlined', `order_num` = 14, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\r\n  \"dataType\": 1,\r\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\r\n  \"timeOut\": 0,\r\n	\"background\": \"#4A90E2\",\r\n	\"linkageConfig\": [],\r\n	\"dataMapping\": [\r\n    {\r\n      \"filed\": \"数值\",\r\n      \"mapping\": \"\"\r\n    }\r\n  ],\r\n  \"turnConfig\": {\r\n    \"url\": \"\"\r\n  },\r\n  \"chartData\": \"文本内容\",\r\n  \"option\": {\r\n    \"card\": {\r\n      \"title\": \"\",\r\n      \"extra\": \"\",\r\n      \"rightHref\": \"\",\r\n      \"size\": \"default\"\r\n    },\r\n    \"body\": {\r\n      \"text\": \"\",\r\n      \"color\": \"#FFFFFF\",\r\n      \"fontWeight\": \"bold\",\r\n      \"marginLeft\": 0,\r\n      \"marginTop\": 0\r\n    }\r\n  }\r\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 10:49:04' WHERE `id` = '100110';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '散点地图', `comp_type` = 'JBubbleMap', `icon` = 'ic:outline-scatter-plot', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"activeKey\": 1,\n  \"dataType\": 1,\n  \"background\": \"#ffffff\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n       \"dataMapping\": [{\n        \"filed\": \"区域\",\n        \"mapping\": \"\"\n    }, {\n        \"filed\": \"数值\",\n        \"mapping\": \"\"\n    }],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"jsConfig\": \"\",\n  \"chartData\": [\n    {\n      \"name\": \"北京\",\n      \"value\": 199\n    },\n    {\n      \"name\": \"天津\",\n      \"value\": 42\n    },\n    {\n      \"name\": \"河北\",\n      \"value\": 102\n    },\n    {\n      \"name\": \"山西\",\n      \"value\": 81\n    },\n    {\n      \"name\": \"内蒙古\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"辽宁\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"吉林\",\n      \"value\": 82\n    },\n    {\n      \"name\": \"黑龙江\",\n      \"value\": 123\n    },\n    {\n      \"name\": \"上海\",\n      \"value\": 24\n    },\n    {\n      \"name\": \"江苏\",\n      \"value\": 92\n    },\n    {\n      \"name\": \"浙江\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"安徽\",\n      \"value\": 109\n    },\n    {\n      \"name\": \"福建\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"江西\",\n      \"value\": 91\n    },\n    {\n      \"name\": \"山东\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"河南\",\n      \"value\": 137\n    },\n    {\n      \"name\": \"湖北\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"湖南\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"重庆\",\n      \"value\": 91\n    },\n    {\n      \"name\": \"四川\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"贵州\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"云南\",\n      \"value\": 83\n    },\n    {\n      \"name\": \"西藏\",\n      \"value\": 9\n    },\n    {\n      \"name\": \"陕西\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"甘肃\",\n      \"value\": 56\n    },\n    {\n      \"name\": \"青海\",\n      \"value\": 10\n    },\n    {\n      \"name\": \"宁夏\",\n      \"value\": 18\n    },\n    {\n      \"name\": \"新疆\",\n      \"value\": 180\n    },\n    {\n      \"name\": \"广东\",\n      \"value\": 123\n    },\n    {\n      \"name\": \"广西\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"海南\",\n      \"value\": 14\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#fff176\",\n    \"barColor2\": \"#fcc02e\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#fcc02e\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#000000\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"left\": 10,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 200,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        1\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": true,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#fff59c\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 10:32:09' WHERE `id` = '100120100';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '柱形地图', `comp_type` = 'JBarMap', `icon` = 'uil:graph-bar', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"dataType\": 1,\n  \"background\": \"#ffffff\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"name\": \"北京\",\n      \"value\": 900\n    },\n    {\n      \"name\": \"山西\",\n      \"value\": 1681\n    },\n    {\n      \"name\": \"内蒙古\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"辽宁\",\n      \"value\": 1667\n    },\n    {\n      \"name\": \"福建\",\n      \"value\": 516\n    },\n    {\n      \"name\": \"江西\",\n      \"value\": 591\n    },\n    {\n      \"name\": \"山东\",\n      \"value\": 419\n    },\n    {\n      \"name\": \"河南\",\n      \"value\": 137\n    },\n    {\n      \"name\": \"云南\",\n      \"value\": 983\n    },\n    {\n      \"name\": \"西藏\",\n      \"value\": 9\n    },\n    {\n      \"name\": \"陕西\",\n      \"value\": 580\n    },\n    {\n      \"name\": \"甘肃\",\n      \"value\": 556\n    },\n    {\n      \"name\": \"海南\",\n      \"value\": 14\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 12,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#3B373700\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#000000\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"tooltip\": {\n      \"trigger\": \"item\",\n      \"show\": false,\n      \"enterable\": true,\n      \"textStyle\": {\n        \"fontSize\": 20,\n        \"color\": \"#fff\"\n      },\n      \"backgroundColor\": \"rgba(0,2,89,0.8)\"\n    },\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"left\": 10,\n      \"show\": true\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"max\": 200,\n      \"seriesIndex\": [\n        0\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"roam\": true,\n      \"aspectScale\": 0.96,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"#37805B\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\r\n        \"emphasis\": {\r\n          \"areaColor\": \"#fff59c\"\r\n        }\n      }\n    },\n    \"series \": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:01' WHERE `id` = '100120102';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '热力地图', `comp_type` = 'JHeatMap', `icon` = 'carbon:heat-map-02', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"dataType\": 1,\n  \"background\": \"#000000\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"name\": \"海门\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"鄂尔多斯\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"招远\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"舟山\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"齐齐哈尔\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"盐城\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"赤峰\",\n      \"value\": 16\n    },\n    {\n      \"name\": \"青岛\",\n      \"value\": 450\n    },\n    {\n      \"name\": \"乳山\",\n      \"value\": 118\n    },\n    {\n      \"name\": \"金昌\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"泉州\",\n      \"value\": 21\n    },\n    {\n      \"name\": \"莱西\",\n      \"value\": 300\n    },\n    {\n      \"name\": \"日照\",\n      \"value\": 121\n    },\n    {\n      \"name\": \"胶南\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"南通\",\n      \"value\": 23\n    },\n    {\n      \"name\": \"拉萨\",\n      \"value\": 321\n    },\n    {\n      \"name\": \"云浮\",\n      \"value\": 444\n    },\n    {\n      \"name\": \"梅州\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"文登\",\n      \"value\": 456\n    },\n    {\n      \"name\": \"上海\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"攀枝花\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"威海\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"承德\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"厦门\",\n      \"value\": 126\n    },\n    {\n      \"name\": \"汕尾\",\n      \"value\": 26\n    },\n    {\n      \"name\": \"潮州\",\n      \"value\": 247\n    },\n    {\n      \"name\": \"丹东\",\n      \"value\": 227\n    },\n    {\n      \"name\": \"太仓\",\n      \"value\": 427\n    },\n    {\n      \"name\": \"曲靖\",\n      \"value\": 327\n    },\n    {\n      \"name\": \"烟台\",\n      \"value\": 28\n    },\n    {\n      \"name\": \"福州\",\n      \"value\": 29\n    },\n    {\n      \"name\": \"瓦房店\",\n      \"value\": 30\n    },\n    {\n      \"name\": \"即墨\",\n      \"value\": 30\n    },\n    {\n      \"name\": \"抚顺\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"玉溪\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"张家口\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"阳泉\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"莱州\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"湖州\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"汕头\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"昆山\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"宁波\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"湛江\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"揭阳\",\n      \"value\": 34\n    },\n    {\n      \"name\": \"荣成\",\n      \"value\": 34\n    },\n    {\n      \"name\": \"连云港\",\n      \"value\": 35\n    },\n    {\n      \"name\": \"葫芦岛\",\n      \"value\": 35\n    },\n    {\n      \"name\": \"常熟\",\n      \"value\": 236\n    },\n    {\n      \"name\": \"东莞\",\n      \"value\": 336\n    },\n    {\n      \"name\": \"河源\",\n      \"value\": 36\n    },\n    {\n      \"name\": \"淮安\",\n      \"value\": 436\n    },\n    {\n      \"name\": \"泰州\",\n      \"value\": 236\n    },\n    {\n      \"name\": \"南宁\",\n      \"value\": 437\n    },\n    {\n      \"name\": \"营口\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"惠州\",\n      \"value\": 337\n    },\n    {\n      \"name\": \"江阴\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"蓬莱\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"韶关\",\n      \"value\": 38\n    },\n    {\n      \"name\": \"嘉峪关\",\n      \"value\": 38\n    },\n    {\n      \"name\": \"广州\",\n      \"value\": 138\n    },\n    {\n      \"name\": \"延安\",\n      \"value\": 138\n    },\n    {\n      \"name\": \"太原\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"清远\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"中山\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"昆明\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"寿光\",\n      \"value\": 440\n    },\n    {\n      \"name\": \"盘锦\",\n      \"value\": 40\n    },\n    {\n      \"name\": \"长治\",\n      \"value\": 41\n    },\n    {\n      \"name\": \"深圳\",\n      \"value\": 41\n    },\n    {\n      \"name\": \"珠海\",\n      \"value\": 42\n    },\n    {\n      \"name\": \"宿迁\",\n      \"value\": 43\n    },\n    {\n      \"name\": \"咸阳\",\n      \"value\": 43\n    },\n    {\n      \"name\": \"铜川\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"平度\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"佛山\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"海口\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"江门\",\n      \"value\": 45\n    },\n    {\n      \"name\": \"章丘\",\n      \"value\": 45\n    },\n    {\n      \"name\": \"肇庆\",\n      \"value\": 46\n    },\n    {\n      \"name\": \"大连\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"临汾\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"吴江\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"石嘴山\",\n      \"value\": 49\n    },\n    {\n      \"name\": \"沈阳\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"苏州\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"茂名\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"嘉兴\",\n      \"value\": 51\n    },\n    {\n      \"name\": \"长春\",\n      \"value\": 51\n    },\n    {\n      \"name\": \"胶州\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"银川\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"张家港\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"三门峡\",\n      \"value\": 53\n    },\n    {\n      \"name\": \"锦州\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"南昌\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"柳州\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"三亚\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"自贡\",\n      \"value\": 156\n    },\n    {\n      \"name\": \"吉林\",\n      \"value\": 156\n    },\n    {\n      \"name\": \"阳江\",\n      \"value\": 257\n    },\n    {\n      \"name\": \"泸州\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"西宁\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"宜宾\",\n      \"value\": 258\n    },\n    {\n      \"name\": \"呼和浩特\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"成都\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"大同\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"镇江\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"桂林\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"张家界\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"宜兴\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"北海\",\n      \"value\": 60\n    },\n    {\n      \"name\": \"西安\",\n      \"value\": 61\n    },\n    {\n      \"name\": \"金坛\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"东营\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"牡丹江\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"遵义\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"绍兴\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"扬州\",\n      \"value\": 64\n    },\n    {\n      \"name\": \"常州\",\n      \"value\": 64\n    },\n    {\n      \"name\": \"潍坊\",\n      \"value\": 65\n    },\n    {\n      \"name\": \"重庆\",\n      \"value\": 66\n    },\n    {\n      \"name\": \"台州\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"南京\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"滨州\",\n      \"value\": 70\n    },\n    {\n      \"name\": \"贵阳\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"无锡\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"本溪\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"克拉玛依\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"渭南\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"马鞍山\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"宝鸡\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"焦作\",\n      \"value\": 75\n    },\n    {\n      \"name\": \"句容\",\n      \"value\": 75\n    },\n    {\n      \"name\": \"北京\",\n      \"value\": 79\n    },\n    {\n      \"name\": \"徐州\",\n      \"value\": 79\n    },\n    {\n      \"name\": \"衡水\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"包头\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"绵阳\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"乌鲁木齐\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"枣庄\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"杭州\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"淄博\",\n      \"value\": 85\n    },\n    {\n      \"name\": \"鞍山\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"溧阳\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"库尔勒\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"安阳\",\n      \"value\": 190\n    },\n    {\n      \"name\": \"开封\",\n      \"value\": 390\n    },\n    {\n      \"name\": \"济南\",\n      \"value\": 292\n    },\n    {\n      \"name\": \"德阳\",\n      \"value\": 393\n    },\n    {\n      \"name\": \"温州\",\n      \"value\": 95\n    },\n    {\n      \"name\": \"九江\",\n      \"value\": 96\n    },\n    {\n      \"name\": \"邯郸\",\n      \"value\": 98\n    },\n    {\n      \"name\": \"临安\",\n      \"value\": 99\n    },\n    {\n      \"name\": \"兰州\",\n      \"value\": 99\n    },\n    {\n      \"name\": \"沧州\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"临沂\",\n      \"value\": 103\n    },\n    {\n      \"name\": \"南充\",\n      \"value\": 104\n    },\n    {\n      \"name\": \"天津\",\n      \"value\": 105\n    },\n    {\n      \"name\": \"富阳\",\n      \"value\": 106\n    },\n    {\n      \"name\": \"泰安\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"诸暨\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"郑州\",\n      \"value\": 113\n    },\n    {\n      \"name\": \"哈尔滨\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"聊城\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"芜湖\",\n      \"value\": 117\n    },\n    {\n      \"name\": \"唐山\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"平顶山\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"邢台\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"德州\",\n      \"value\": 120\n    },\n    {\n      \"name\": \"济宁\",\n      \"value\": 120\n    },\n    {\n      \"name\": \"荆州\",\n      \"value\": 127\n    },\n    {\n      \"name\": \"宜昌\",\n      \"value\": 130\n    },\n    {\n      \"name\": \"义乌\",\n      \"value\": 132\n    },\n    {\n      \"name\": \"丽水\",\n      \"value\": 133\n    },\n    {\n      \"name\": \"洛阳\",\n      \"value\": 134\n    },\n    {\n      \"name\": \"秦皇岛\",\n      \"value\": 136\n    },\n    {\n      \"name\": \"株洲\",\n      \"value\": 143\n    },\n    {\n      \"name\": \"石家庄\",\n      \"value\": 147\n    },\n    {\n      \"name\": \"莱芜\",\n      \"value\": 148\n    },\n    {\n      \"name\": \"常德\",\n      \"value\": 152\n    },\n    {\n      \"name\": \"保定\",\n      \"value\": 153\n    },\n    {\n      \"name\": \"湘潭\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"金华\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"岳阳\",\n      \"value\": 169\n    },\n    {\n      \"name\": \"长沙\",\n      \"value\": 175\n    },\n    {\n      \"name\": \"衢州\",\n      \"value\": 177\n    },\n    {\n      \"name\": \"廊坊\",\n      \"value\": 193\n    },\n    {\n      \"name\": \"菏泽\",\n      \"value\": 194\n    },\n    {\n      \"name\": \"合肥\",\n      \"value\": 229\n    },\n    {\n      \"name\": \"武汉\",\n      \"value\": 273\n    },\n    {\n      \"name\": \"大庆\",\n      \"value\": 279\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#3B373700\"\n    },\n    \"heat\": {\n      \"pointSize\": 15,\n      \"blurSize\": 20,\n      \"maxOpacity\": 1\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#E08D8D\",\n        \"#ff9800\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#df2425\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"left\": 10,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"visualMap\": {\n      \"show\": true,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 200,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        1\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": true,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#fff59c\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:24' WHERE `id` = '100120106';
UPDATE `onl_drag_comp` SET `parent_id` = '200300', `comp_name` = '多色仪表盘', `comp_type` = 'JColorGauge', `icon` = 'mdi:gauge', `order_num` = NULL, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/gauge\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"名称\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"使用率\",\n      \"value\": 4\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"多色仪表盘\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			}\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"formatter\": \"{a} <br/>{b} : {c}%\"\n    },\n    \"series\": [\n      {\n        \"anchor\": {\n          \"itemStyle\": {\n            \"color\": \"#FAC858\"\n          }\n        },\n        \"pointer\": {\n          \"width\": 8\n        },\n        \"axisLabel\": {\n          \"show\": true,\n          \"fontSize\": 12\n        },\n        \"axisLine\": {\n          \"lineStyle\": {\n            \"width\": 10,\n            \"color\": [\n              [\n                0.25,\n                \"#FF6E76\"\n              ],\n              [\n                0.5,\n                \"#FDDD60\"\n              ],\n              [\n                1,\n                \"#58D9F9\"\n              ]\n            ]\n          }\n        },\n        \"splitLine\": {\n          \"length\": 12,\n          \"lineStyle\": {\n            \"color\": \"#eee\",\n            \"width\": 4\n          }\n        },\n        \"axisTick\": {\n          \"show\": true,\n          \"lineStyle\": {\n            \"color\": \"#eee\"\n          }\n        },\n        \"title\": {\n          \"fontSize\": 14\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-03-08 18:12:13', `update_by` = 'jeecg', `update_time` = '2022-08-23 14:20:20' WHERE `id` = '1501138710950854657';
UPDATE `onl_drag_comp` SET `parent_id` = '200200', `comp_name` = '背景柱形图', `comp_type` = 'JBackgroundBar', `icon` = 'ic:baseline-bar-chart', `order_num` = 2, `type_id` = NULL, `comp_config` = '{\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"苹果\",\n      \"value\": 1000879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"三星\",\n      \"value\": 3400879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"小米\",\n      \"value\": 2300879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"oppo\",\n      \"value\": 5400879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"vivo\",\n      \"value\": 3400879,\n      \"type\": \"手机品牌\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"背景柱形图\",\n      \"show\": true,\n      \"textStyle\": {\n        \"color\": \"#464646\",\r\n				\"fontWeight\":\"normal\"\n      }\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"yAxis\": {\n      \"splitLine\": {\n        \"interval\": 2\n      },\n      \"lineStyle\": {\n        \"color\": \"#f3f3f3\"\n      }\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"type\": \"bar\",\n        \"barWidth\": 40,\n        \"itemStyle\": {\n          \"color\": \"#5470c6\",\n          \"borderRadius\": 0\n        },\n        \"showBackground\": true,\n        \"backgroundStyle\": {\n          \"color\": \"#eee\"\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-15 14:02:45', `update_by` = 'admin', `update_time` = '2022-08-04 19:18:56' WHERE `id` = '1536952329568276481';
UPDATE `onl_drag_comp` SET `parent_id` = '200200', `comp_name` = '对比柱形图', `comp_type` = 'JMultipleBar', `icon` = 'material-symbols:grouped-bar-chart', `order_num` = 5, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/stackedBar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"1991\",\n      \"value\": 3,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 4,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 3.5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 4.9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 6,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 7,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 13,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1991\",\n      \"value\": 3,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 4,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 3.5,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 5,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 4.9,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 6,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 7,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 9,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 13,\n      \"type\": \"Bor\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"多数据对比柱形图\",\n      \"show\": true,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			}\n    },\n    \"grid\": {\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-15 15:13:57', `update_by` = 'admin', `update_time` = '2022-08-04 19:19:06' WHERE `id` = '1536970245843996673';
UPDATE `onl_drag_comp` SET `parent_id` = '200200', `comp_name` = '正负条形图', `comp_type` = 'JNegativeBar', `icon` = 'mdi:chart-gantt', `order_num` = 6, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/stackedBar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"周一\",\n      \"value\": 200,\n      \"type\": \"利润\"\n    },\n    {\n      \"name\": \"周二\",\n      \"value\": 170,\n      \"type\": \"利润\"\n    },\n    {\n      \"name\": \"周三\",\n      \"value\": 240,\n      \"type\": \"利润\"\n    },\n    {\n      \"name\": \"周四\",\n      \"value\": 244,\n      \"type\": \"利润\"\n    },\n    {\n      \"name\": \"周五\",\n      \"value\": 200,\n      \"type\": \"利润\"\n    },\n    {\n      \"name\": \"周六\",\n      \"value\": 220,\n      \"type\": \"利润\"\n    },\n    {\n      \"name\": \"周日\",\n      \"value\": 210,\n      \"type\": \"利润\"\n    },\n    {\n      \"name\": \"周一\",\n      \"value\": 320,\n      \"type\": \"收入\"\n    },\n    {\n      \"name\": \"周二\",\n      \"value\": 302,\n      \"type\": \"收入\"\n    },\n    {\n      \"name\": \"周三\",\n      \"value\": 341,\n      \"type\": \"收入\"\n    },\n    {\n      \"name\": \"周四\",\n      \"value\": 374,\n      \"type\": \"收入\"\n    },\n    {\n      \"name\": \"周五\",\n      \"value\": 390,\n      \"type\": \"收入\"\n    },\n    {\n      \"name\": \"周六\",\n      \"value\": 450,\n      \"type\": \"收入\"\n    },\n    {\n      \"name\": \"周日\",\n      \"value\": 420,\n      \"type\": \"收入\"\n    },\n    {\n      \"name\": \"周一\",\n      \"value\": -120,\n      \"type\": \"支出\"\n    },\n    {\n      \"name\": \"周二\",\n      \"value\": -132,\n      \"type\": \"支出\"\n    },\n    {\n      \"name\": \"周三\",\n      \"value\": -101,\n      \"type\": \"支出\"\n    },\n    {\n      \"name\": \"周四\",\n      \"value\": -134,\n      \"type\": \"支出\"\n    },\n    {\n      \"name\": \"周五\",\n      \"value\": -190,\n      \"type\": \"支出\"\n    },\n    {\n      \"name\": \"周六\",\n      \"value\": -230,\n      \"type\": \"支出\"\n    },\n    {\n      \"name\": \"周日\",\n      \"value\": -210,\n      \"type\": \"支出\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"正负条形图\",\n      \"show\": true,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			}\n    },\n    \"xAxis\": {\n      \"type\": \"value\"\n    },\n    \"grid\": {\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-15 15:41:17', `update_by` = 'admin', `update_time` = '2022-08-04 19:19:14' WHERE `id` = '1536977123995045890';
UPDATE `onl_drag_comp` SET `parent_id` = '1537002903949037570', `comp_name` = '对比折线图', `comp_type` = 'JMultipleLine', `icon` = 'ant-design:line-chart-outlined', `order_num` = 4, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/stackedBar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"1991\",\n      \"value\": 3,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 4,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 3.5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 4.9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 6,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 7,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 13,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1991\",\n      \"value\": 6,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 8,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 7,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 10,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 11,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 4,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 20,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 16,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 9,\n      \"type\": \"Bor\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"多数据对比折线图\",\n      \"show\": true,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			}\n    },\n    \"grid\": {\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-15 17:29:50', `update_by` = 'admin', `update_time` = '2022-08-04 19:20:20' WHERE `id` = '1537004441727684609';
UPDATE `onl_drag_comp` SET `parent_id` = '1537002903949037570', `comp_name` = '阶梯折线图', `comp_type` = 'JStepLine', `icon` = 'mdi:chart-line', `order_num` = 3, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1000,\n      \"name\": \"联想\"\n    },\n    {\n      \"value\": 7350,\n      \"name\": \"小米\"\n    },\n    {\n      \"value\": 5800,\n      \"name\": \"华为\"\n    },\n    {\n      \"value\": 6000,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 3000,\n      \"name\": \"戴尔\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"阶梯折线图\",\n      \"subtext\": \"\",\n      \"left\": \"left\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			}\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"step\": \"middle\",\n        \"type\": \"line\"\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-16 11:59:19', `update_by` = 'admin', `update_time` = '2022-08-04 19:20:29' WHERE `id` = '1537283654863044610';
UPDATE `onl_drag_comp` SET `parent_id` = '1537002903949037570', `comp_name` = '平滑折线图', `comp_type` = 'JSmoothLine', `icon` = 'mdi:chart-bell-curve', `order_num` = 2, `type_id` = NULL, `comp_config` = '{\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1000,\n      \"name\": \"联想\"\n    },\n    {\n      \"value\": 7350,\n      \"name\": \"小米\"\n    },\n    {\n      \"value\": 5800,\n      \"name\": \"华为\"\n    },\n    {\n      \"value\": 6000,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 3000,\n      \"name\": \"戴尔\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"平滑折线图\",\n      \"subtext\": \"\",\n      \"left\": \"left\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			}\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"smooth\": true,\n        \"type\": \"line\"\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-16 12:00:49', `update_by` = 'admin', `update_time` = '2022-08-04 19:19:43' WHERE `id` = '1537284032572702721';
UPDATE `onl_drag_comp` SET `parent_id` = '1537764165146476546', `comp_name` = '普通散点图', `comp_type` = 'JScatter', `icon` = 'mdi:chart-scatter-plot', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": 200,\n      \"value\": 300\n    },\n    {\n      \"name\": 400,\n      \"value\": 500\n    },\n    {\n      \"name\": 150,\n      \"value\": 320\n    },\n    {\n      \"name\": 320,\n      \"value\": 320\n    },\n    {\n      \"name\": 170,\n      \"value\": 300\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"基础散点图\",\n      \"show\": true,\n      \"textStyle\": {\n        \"color\": \"#464646\",\r\n				\"fontWeight\":\"normal\"\n      }\n    },\n    \"tooltip\": {\n      \"trigger\": \"item\",\n      \"formatter\": \"x:{b}<br/>y:{c}\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"yAxis\": {\n      \"splitLine\": {\n        \"interval\": 2\n      },\n      \"lineStyle\": {\n        \"color\": \"#f3f3f3\"\n      }\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"type\": \"scatter\",\n        \"symbolSize\": 20,\r\n        \"itemStyle\": {\r\n          \"color\": \"#64b5f6\"\r\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-16 14:16:07', `update_by` = NULL, `update_time` = '2022-06-16 17:10:52' WHERE `id` = '1537318081257291777';
UPDATE `onl_drag_comp` SET `parent_id` = '1537764868216684545', `comp_name` = '金字塔漏斗图', `comp_type` = 'JPyramidFunnel', `icon` = 'icon-park-outline:children-pyramid', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/funnel\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1000,\n      \"name\": \"直接访问\"\n    },\n    {\n      \"value\": 200,\n      \"name\": \"邮件营销\"\n    },\n    {\n      \"value\": 400,\n      \"name\": \"联盟广告\"\n    },\n    {\n      \"value\": 600,\n      \"name\": \"网页查询\"\n    },\n    {\n      \"value\": 800,\n      \"name\": \"广告点击\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"基础漏斗图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"grid\": {\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"item\",\n      \"formatter\": \"{a} <br/>{b} : {c}%\"\n    },\n    \"legend\": {\n      \"orient\": \"horizontal\"\n    },\n    \"series\": [\n      {\n        \"name\": \"Funnel\",\n        \"type\": \"funnel\",\n        \"left\": \"10%\",\n        \"width\": \"80%\",\n        \"sort\": \"ascending\",\n        \"gap\": 2,\n        \"label\": {\n          \"show\": true,\n          \"position\": \"inside\"\n        },\n        \"labelLine\": {\n          \"length\": 10,\n          \"lineStyle\": {\n            \"width\": 1,\n            \"type\": \"solid\"\n          }\n        },\n        \"itemStyle\": {\n          \"borderColor\": \"#fff\",\n          \"borderWidth\": 1\n        },\n        \"emphasis\": {\n          \"label\": {\n            \"fontSize\": 20\n          }\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-16 14:17:31', `update_by` = NULL, `update_time` = '2022-06-16 14:59:44' WHERE `id` = '1537318433201340417';
UPDATE `onl_drag_comp` SET `parent_id` = '200200', `comp_name` = '折柱图', `comp_type` = 'JMixLineBar', `icon` = 'ic:baseline-bar-chart', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/stackedBar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"seriesType\": [\n    {\n      \"series\": \"降水量\",\n      \"type\": \"bar\"\n    },\n    {\n      \"series\": \"温度\",\n      \"type\": \"line\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"1991\",\n      \"value\": 110,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 130,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 113.5,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 150,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 240.9,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 160,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 97,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 290,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 230,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1991\",\n      \"value\": 33,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 35,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 37,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 35,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 34.9,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 36,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 37,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 39,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 33,\n      \"type\": \"温度\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"折柱图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"grid\": {\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-16 14:46:16', `update_by` = 'admin', `update_time` = '2022-08-04 19:19:20' WHERE `id` = '1537325666777710594';
UPDATE `onl_drag_comp` SET `parent_id` = '1537764165146476546', `comp_name` = '气泡图', `comp_type` = 'JBubble', `icon` = 'mdi:chart-scatter-plot', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/stackedBar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": 4,\n      \"value\": 3,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 5,\n      \"value\": 4,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 6,\n      \"value\": 3.5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 7,\n      \"value\": 5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 8,\n      \"value\": 4.9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 9,\n      \"value\": 6,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 10,\n      \"value\": 7,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 11,\n      \"value\": 9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 12,\n      \"value\": 13,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": 11,\n      \"value\": 6,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": 10,\n      \"value\": 8,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": 9,\n      \"value\": 7,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": 8,\n      \"value\": 10,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": 7,\n      \"value\": 11,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": 6,\n      \"value\": 4,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": 10,\n      \"value\": 20,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": 8,\n      \"value\": 16,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": 7,\n      \"value\": 9,\n      \"type\": \"Bor\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"气泡图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"grid\": {\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-16 18:56:41', `update_by` = NULL, `update_time` = '2022-06-16 18:59:43' WHERE `id` = '1537388686279196673';
UPDATE `onl_drag_comp` SET `parent_id` = '1537773378102984706', `comp_name` = '圆形雷达图', `comp_type` = 'JCircleRadar', `icon` = 'tabler:radar', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 75,\n      \"name\": \"得分\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 65,\n      \"name\": \"篮板\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 55,\n      \"name\": \"防守\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 74,\n      \"name\": \"失误\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 38,\n      \"name\": \"盖帽\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 88,\n      \"name\": \"三分\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"圆形雷达图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"radar\": [\n      {\n        \"indicator\": []\n      }\n    ],\n    \"series\": [\n      {\n        \"type\": \"radar\",\n        \"data\": []\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-17 20:24:46', `update_by` = NULL, `update_time` = '2022-06-27 16:56:51' WHERE `id` = '1537773244027863041';
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '富文本', `comp_type` = 'JDragEditor', `icon` = 'ant-design:credit-card-twotone', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"timeOut\": 0,\n  \"chartData\": \"富文本内容...\"\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-06-28 19:25:25', `update_by` = 'jeecg', `update_time` = '2022-07-29 15:48:14' WHERE `id` = '1541744572086898690';
UPDATE `onl_drag_comp` SET `parent_id` = '200200', `comp_name` = '基础柱形图', `comp_type` = 'JBar', `icon` = 'ic:baseline-bar-chart', `order_num` = NULL, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"苹果\",\n      \"value\": 1000879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"三星\",\n      \"value\": 3400879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"小米\",\n      \"value\": 2300879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"oppo\",\n      \"value\": 5400879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"vivo\",\n      \"value\": 3400879,\n      \"type\": \"手机品牌\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"top\":90, \n      \"bottom\": 115,\r\n			\"containLabel\": true\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"基础柱形图\",\n      \"show\": true,\n      \"textStyle\": {\n        \"color\": \"#464646\",\r\n				\"fontWeight\":\"normal\"\n      }\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"yAxis\": {\n      \"splitLine\": {\n        \"interval\": 2\n      },\n      \"lineStyle\": {\n        \"color\": \"#f3f3f3\"\n      }\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"type\": \"bar\",\n        \"barWidth\": 40,\n        \"itemStyle\": {\n          \"color\": \"#64b5f6\",\n          \"borderRadius\": 0\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-04 19:18:21' WHERE `id` = '200200201';
UPDATE `onl_drag_comp` SET `parent_id` = '200200', `comp_name` = '堆叠柱形图', `comp_type` = 'JStackBar', `icon` = 'ic:outline-stacked-bar-chart', `order_num` = NULL, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"background\": \"#ffffff\",\n  \"url\": \"http://api.jeecg.com/mock/26/stackedBar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"1991\",\n      \"value\": 3,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 4,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 3.5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 4.9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 6,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 7,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 13,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1991\",\n      \"value\": 3,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 4,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 3.5,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 5,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 4.9,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 6,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 7,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 9,\n      \"type\": \"Bor\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 13,\n      \"type\": \"Bor\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"堆叠柱形图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"grid\": {\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2021-12-28 16:18:31', `update_by` = 'admin', `update_time` = '2022-08-04 19:18:32' WHERE `id` = '200200202';
UPDATE `onl_drag_comp` SET `parent_id` = '200200', `comp_name` = '动态柱形图', `comp_type` = 'JDynamicBar', `icon` = 'ph:chart-bar-horizontal-light', `order_num` = NULL, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/stackedBar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"1991\",\n      \"value\": 131,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 141,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 31.5,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 53,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 41.9,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 61,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 17,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 19,\n      \"type\": \"Lon\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 113,\n      \"type\": \"Lon\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"动态柱形图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			}\n    },\n    \"grid\": {\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-04 19:18:47' WHERE `id` = '200200203';
UPDATE `onl_drag_comp` SET `parent_id` = '200', `comp_name` = '饼图', `comp_type` = 'JPie', `icon` = 'ant-design:pie-chart-outlined', `order_num` = 2, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": -1,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1048,\n      \"name\": \"vivo\"\n    },\n    {\n      \"value\": 735,\n      \"name\": \"oppo\"\n    },\n    {\n      \"value\": 580,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 484,\n      \"name\": \"小米\"\n    },\n    {\n      \"value\": 300,\n      \"name\": \"三星\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"title\": {\n      \"text\": \"基础饼图\",\n      \"subtext\": \"\",\n      \"left\": \"left\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"item\"\n    },\n    \"legend\": {\n      \"orient\": \"vertical\"\n    },\n    \"series\": [\n      {\n        \"name\": \"\",\n        \"type\": \"pie\",\n        \"radius\": \"50%\",\n        \"data\": [],\n        \"emphasis\": {\n          \"itemStyle\": {\n            \"shadowBlur\": 10,\n            \"shadowOffsetX\": 0,\n            \"shadowColor\": \"rgba(0, 0, 0, 0.5)\"\n          }\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-05-23 14:07:29' WHERE `id` = '200201';
UPDATE `onl_drag_comp` SET `parent_id` = '1537002903949037570', `comp_name` = '基础折线图', `comp_type` = 'JLine', `icon` = 'teenyicons:area-chart-outline', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1000,\n      \"name\": \"联想\"\n    },\n    {\n      \"value\": 7350,\n      \"name\": \"小米\"\n    },\n    {\n      \"value\": 5800,\n      \"name\": \"华为\"\n    },\n    {\n      \"value\": 6000,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 3000,\n      \"name\": \"戴尔\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115,\n          \"top\":90\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"基础折线图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"subtext\": \"\",\n      \"left\": 10\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"type\": \"line\",\r\n         \"itemStyle\": {\r\n          \"color\": \"#64b5f6\"\r\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-04 19:20:12' WHERE `id` = '200202';
UPDATE `onl_drag_comp` SET `parent_id` = '1537773378102984706', `comp_name` = '普通雷达图', `comp_type` = 'JRadar', `icon` = 'ant-design:radar-chart-outlined', `order_num` = 4, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 75,\n      \"name\": \"得分\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 65,\n      \"name\": \"篮板\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 55,\n      \"name\": \"防守\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 74,\n      \"name\": \"失误\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 38,\n      \"name\": \"盖帽\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    },\n    {\n      \"value\": 88,\n      \"name\": \"三分\",\n      \"type\": \"NBA\",\n      \"max\": 100\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"基础雷达图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"radar\": [\n      {\n        \"indicator\": []\n      }\n    ],\n    \"series\": [\n      {\n        \"type\": \"radar\",\n        \"data\": []\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-06-17 20:25:47' WHERE `id` = '200204';
UPDATE `onl_drag_comp` SET `parent_id` = '200', `comp_name` = '环形图', `comp_type` = 'JRing', `icon` = 'mdi:chart-donut', `order_num` = 5, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1048,\n      \"name\": \"oppo\"\n    },\n    {\n      \"value\": 735,\n      \"name\": \"vivo\"\n    },\n    {\n      \"value\": 580,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 484,\n      \"name\": \"小米\"\n    },\n    {\n      \"value\": 300,\n      \"name\": \"三星\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"title\": {\n      \"text\": \"基础环形图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"item\"\n    },\n    \"series\": [\n      {\n        \"name\": \"Access From\",\n        \"type\": \"pie\",\n        \"radius\": [\n          \"40%\",\n          \"70%\"\n        ],\n        \"avoidLabelOverlap\": false,\n        \"label\": {\n          \"show\": false,\n          \"position\": \"center\"\n        },\n        \"emphasis\": {\n          \"label\": {\n            \"show\": true,\n            \"fontWeight\": \"bold\",\r\n						\"fontSize\": 14\n          }\n        },\n        \"labelLine\": {\n          \"show\": false\n        },\n        \"data\": []\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-22 19:05:11' WHERE `id` = '200205';
UPDATE `onl_drag_comp` SET `parent_id` = '200', `comp_name` = '面积图', `comp_type` = 'JArea', `icon` = 'teenyicons:area-chart-solid', `order_num` = 6, `type_id` = NULL, `comp_config` = '{\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": -1,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 1048,\n      \"name\": \"华为\"\n    },\n    {\n      \"value\": 605,\n      \"name\": \"vivo\"\n    },\n    {\n      \"value\": 580,\n      \"name\": \"oppo\"\n    },\n    {\n      \"value\": 484,\n      \"name\": \"苹果\"\n    },\n    {\n      \"value\": 300,\n      \"name\": \"小米\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"基础面积图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"xAxis\": {\n      \"boundaryGap\": false,\n      \"data\": []\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"type\": \"line\",\n        \"areaStyle\": {}\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-22 19:04:01' WHERE `id` = '200206';
UPDATE `onl_drag_comp` SET `parent_id` = '1537764868216684545', `comp_name` = '普通漏斗图', `comp_type` = 'JFunnel', `icon` = 'ant-design:funnel-plot-filled', `order_num` = 8, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/funnel\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"value\": 335,\n      \"name\": \"直接访问\"\n    },\n    {\n      \"value\": 310,\n      \"name\": \"邮件营销\"\n    },\n    {\n      \"value\": 234,\n      \"name\": \"联盟广告\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"基础漏斗图\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"grid\": {\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"item\",\n      \"formatter\": \"{a} <br/>{b} : {c}%\"\n    },\n    \"legend\": {\n      \"orient\": \"horizontal\"\n    },\n    \"series\": [\n      {\n        \"name\": \"Funnel\",\n        \"type\": \"funnel\",\n        \"left\": \"10%\",\n        \"width\": \"80%\",\n        \"sort\": \"descending\",\n        \"gap\": 2,\n        \"label\": {\n          \"show\": true,\n          \"position\": \"inside\"\n        },\n        \"labelLine\": {\n          \"length\": 10,\n          \"lineStyle\": {\n            \"width\": 1,\n            \"type\": \"solid\"\n          }\n        },\n        \"itemStyle\": {\n          \"borderColor\": \"#fff\",\n          \"borderWidth\": 1\n        },\n        \"emphasis\": {\n          \"label\": {\n            \"fontSize\": 20\n          }\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = '2022-01-06 14:38:04', `update_by` = 'admin', `update_time` = '2022-06-17 19:51:26' WHERE `id` = '200208';
UPDATE `onl_drag_comp` SET `parent_id` = '200300', `comp_name` = '基础仪表盘', `comp_type` = 'JGauge', `icon` = 'mdi:gauge', `order_num` = NULL, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/gauge\",\n  \"timeOut\": -1,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"名称\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"min\": 1,\n      \"max\": 10,\n      \"label\": \"名称\",\n      \"value\": 4,\n      \"unit\": \"%\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"标准仪表盘\"\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"formatter\": \"{a} <br/>{b} : {c}%\"\n    },\n    \"grid\": {\n      \"bottom\": 115\n    },\n    \"series\": [\n      {\n        \"axisLabel\": {\n          \"show\": true,\n          \"fontSize\": 12\n        },\n        \"detail\": {\n          \"fontSize\": 25,\n          \"formatter\": \"{value}\"\n        },\n        \"splitLine\": {\n          \"length\": 12,\n          \"lineStyle\": {\n            \"color\": \"#eee\",\n            \"width\": 4\n          }\n        },\n        \"axisTick\": {\n          \"show\": true,\n          \"lineStyle\": {\n            \"color\": \"#eee\"\n          }\n        },\n        \"progress\": {\n          \"show\": true\n        },\n        \"data\": [],\r\n \"itemStyle\": {\r\n          \"color\": \"#64b5f6\"\r\n        },\n        \"type\": \"gauge\"\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2021-12-26 18:32:05', `update_by` = 'admin', `update_time` = '2022-04-24 10:57:39' WHERE `id` = '200211';
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '数值', `comp_type` = 'JNumber', `icon` = 'ant-design:field-number-outlined', `order_num` = 14, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\r\n  \"dataType\": 1,\r\n  \"timeOut\": 0,\r\n  \"turnConfig\": {\r\n    \"url\": \"\"\r\n  },\r\n  \"chartData\": {\r\n    \"value\": \"1024\"\r\n  },\r\n  \"analysis\": {\r\n    \"isCompare\": false,\r\n    \"compareType\": \"\",\r\n    \"trendType\": \"1\"\r\n  },\r\n  \"option\": {\r\n    \"isCompare\": false,\r\n    \"trendType\": \"1\",\r\n    \"card\": {\r\n      \"title\": \"未命名标题\",\r\n      \"extra\": \"\",\r\n      \"rightHref\": \"\",\r\n      \"size\": \"small\",\r\n      \"textStyle\": {\r\n        \"fontSize\": 18,\r\n        \"fontWeight\": \"bold\",\r\n        \"color\": \"#464646\"\r\n      }\r\n    },\r\n    \"body\": {\r\n      \"text\": \"\",\r\n      \"color\": \"#000000\",\r\n      \"fontWeight\": \"bold\"\r\n    }\r\n  }\r\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-09-01 15:10:03', `update_by` = 'jeecg', `update_time` = '2022-09-02 16:52:23' WHERE `id` = '725214423934730240';
UPDATE `onl_drag_comp` SET `parent_id` = '1537002903949037570', `comp_name` = '双轴图', `comp_type` = 'DoubleLineBar', `icon` = 'material-symbols:ssid-chart', `order_num` = 5, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/26/stackedBar\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"分组\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"seriesType\": [\n    {\n      \"series\": \"降水量\",\n      \"type\": \"bar\"\n    },\n    {\n      \"series\": \"温度\",\n      \"type\": \"line\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"1991\",\n      \"value\": 110,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 130,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 113.5,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 150,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 240.9,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 160,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 97,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 290,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 230,\n      \"type\": \"降水量\"\n    },\n    {\n      \"name\": \"1991\",\n      \"value\": 33,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1992\",\n      \"value\": 35,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1993\",\n      \"value\": 37,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1994\",\n      \"value\": 35,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1995\",\n      \"value\": 34.9,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1996\",\n      \"value\": 36,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1997\",\n      \"value\": 37,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1998\",\n      \"value\": 39,\n      \"type\": \"温度\"\n    },\n    {\n      \"name\": \"1999\",\n      \"value\": 33,\n      \"type\": \"温度\"\n    }\n  ],\n  \"option\": {\n    \"title\": {\n      \"text\": \"双轴图\",\n      \"show\": true,\n      \"textStyle\": {\n        \"color\": \"#000\",\n        \"fontWeight\": \"normal\",\n        \"fontSize\": \"14\"\n      }\n    },\n    \"legend\": {\n      \"t\": 4\n    },\n    \"grid\": {\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"yAxis\": [\n      {\n        \"type\": \"value\"\n      },\n      {\n        \"type\": \"value\"\n      }\n    ],\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-09-05 19:05:53', `update_by` = 'ldd', `update_time` = '2022-12-06 18:11:10' WHERE `id` = '726723325897637888';
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '自定义按钮', `comp_type` = 'JCustomButton', `icon` = 'teenyicons:button-outline', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\r\n		\"w\": 12,\r\n		\"h\": 30,\r\n    \"dataType\": 1,\r\n    \"url\": \"\",\r\n    \"timeOut\": 0,\r\n    \"chartData\": [\r\n        {\r\n            \"btnId\": \"74591654852155\",\r\n            \"title\": \"销售订单\",\r\n            \"icon\": \"ant-design:calendar-twotone\",\r\n            \"color\": \"#ED4B82\",\r\n            \"operationType\": \"1\",\r\n            \"worksheet\": \"\",\r\n            \"view\": \"\",\r\n            \"defVal\": [\r\n                \r\n            ],\r\n            \"customPage\": \"\",\r\n            \"href\": {\r\n                \"url\": \"\",\r\n                \"isParam\": false,\r\n                \"params\": [\r\n                    \r\n                ]\r\n            },\r\n            \"openMode\": \"2\",\r\n            \"bizFlow\": \"\",\r\n            \"click\": {\r\n                \"type\": \"1\",\r\n                \"message\": {\r\n                    \"title\": \"你确认执行此操作吗？\",\r\n                    \"okText\": \"确认\",\r\n                    \"cancelText\": \"取消\"\r\n                }\r\n            }\r\n        }\r\n    ],\r\n    \"option\": {\r\n        \"title\": \"\",\r\n        \"btnType\": \"button\",\r\n        \"btnStyle\": \"solid\",\r\n        \"btnWidth\": \"custom\",\r\n        \"btnDirection\": \"column\",\r\n        \"rowNum\": 4\r\n    }\r\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-09-09 15:21:08', `update_by` = 'jeecg', `update_time` = '2022-09-09 15:41:56' WHERE `id` = '728116316742778880';
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '查询条件', `comp_type` = 'JFilterQuery', `icon` = 'ant-design:filter-filled', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\r\n	\"w\": 12,\r\n	\"h\": 12,\r\n	\"dataType\": 1,\r\n	\"url\": \"\",\r\n	\"timeOut\": 0,\r\n	\"chartData\": []\r\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-09-09 15:21:08', `update_by` = 'jeecg', `update_time` = '2022-09-09 15:41:56' WHERE `id` = '72811631742776660';
UPDATE `onl_drag_comp` SET `parent_id` = '100102', `comp_name` = '透视表', `comp_type` = 'JPivotTable', `icon` = 'ant-design:table-outlined', `order_num` = 13, `type_id` = NULL, `comp_config` = '{\n  \"w\": 24,\n  \"h\": 42,\n  \"dataType\": 1,\n  \"timeOut\": 0,\n  \"chartData\": {\n    \"x\": [\n      {\n        \"62eb2e00c349cde9883d3c1c\": [\n          \"测试1\",\n          \"测试1\",\n          \"测试2\",\n          \"测试3\"\n        ]\n      },\n      {\n        \"62f37518df6db6d3e0c9b7ad\": [\n          \"1\",\n          \"2\",\n          \"3\",\n          \"4\"\n        ]\n      }\n    ],\n    \"data\": [\n      {\n        \"y\": [\n          \"2022/09\",\n          \"2022\"\n        ],\n        \"t_id\": \"62f37456cf07c28f9312dd13\",\n        \"data\": [\n          111,\n          null,\n          null,\n          null\n        ],\n        \"sum\": 111,\n        \"summary_col\": false\n      },\n      {\n        \"y\": [\n          \"2022/09\",\n          \"2022\"\n        ],\n        \"t_id\": \"62f37456cf07c28f9312dd14\",\n        \"data\": [\n          444,\n          null,\n          null,\n          null\n        ],\n        \"sum\": 444,\n        \"summary_col\": false\n      },\n      {\n        \"y\": [\n          \"2022/08\",\n          \"2022\"\n        ],\n        \"t_id\": \"62f37456cf07c28f9312dd13\",\n        \"data\": [\n          null,\n          222,\n          333,\n          444\n        ],\n        \"sum\": 999,\n        \"summary_col\": false\n      },\n      {\n        \"y\": [\n          \"2022/08\",\n          \"2022\"\n        ],\n        \"t_id\": \"62f37456cf07c28f9312dd14\",\n        \"data\": [\n          null,\n          333,\n          222,\n          111\n        ],\n        \"sum\": 666,\n        \"summary_col\": false\n      },\n      {\n        \"y\": [],\n        \"t_id\": \"62f37456cf07c28f9312dd13\",\n        \"data\": [\n          111,\n          222,\n          333,\n          444\n        ],\n        \"sum\": 278,\n        \"summary_col\": true\n      },\n      {\n        \"y\": [],\n        \"t_id\": \"62f37456cf07c28f9312dd14\",\n        \"data\": [\n          444,\n          333,\n          222,\n          111\n        ],\n        \"sum\": 1110,\n        \"summary_col\": true\n      }\n    ]\n  },\n  \"option\": {\r\n		\"card\": {\r\n			\"title\": \"未命名标题\",\r\n			\"extra\": \"\",\r\n			\"rightHref\": \"\",\r\n			\"size\": \"default\"\r\n		}\r\n  }\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-09-13 14:21:21', `update_by` = 'jeecg', `update_time` = '2022-09-13 20:13:32' WHERE `id` = '729550825967222784';

-- 修改文本/数值配置项
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '文本', `comp_type` = 'JText', `icon` = 'ant-design:font-colors-outlined', `order_num` = 14, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\r\n  \"dataType\": 1,\r\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\r\n  \"timeOut\": 0,\r\n	\"background\": \"#4A90E2\",\r\n	\"linkageConfig\": [],\r\n	\"dataMapping\": [\r\n    {\r\n      \"filed\": \"数值\",\r\n      \"mapping\": \"\"\r\n    }\r\n  ],\r\n  \"turnConfig\": {\r\n    \"url\": \"\"\r\n  },\r\n  \"chartData\": \"文本内容\",\r\n  \"option\": {\r\n    \"card\": {\r\n      \"title\": \"\",\r\n      \"extra\": \"\",\r\n      \"rightHref\": \"\",\r\n      \"size\": \"default\"\r\n    },\r\n    \"body\": {\r\n      \"text\": \"\",\r\n      \"color\": \"#FFFFFF\",\r\n      \"fontWeight\": \"bold\",\r\n      \"marginLeft\": 0,\r\n      \"marginTop\": 0\r\n    }\r\n  }\r\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 10:49:04' WHERE `id` = '100110';
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '数值', `comp_type` = 'JNumber', `icon` = 'ant-design:field-number-outlined', `order_num` = 14, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\r\n  \"dataType\": 1,\r\n  \"timeOut\": 0,\r\n  \"turnConfig\": {\r\n    \"url\": \"\"\r\n  },\r\n	\"linkageConfig\": [],\r\n	\"dataMapping\": [\r\n    {\r\n      \"filed\": \"数值\",\r\n      \"mapping\": \"\"\r\n    }\r\n  ],\r\n  \"chartData\": {\r\n    \"value\": \"1024\"\r\n  },\r\n  \"analysis\": {\r\n    \"isCompare\": false,\r\n    \"compareType\": \"\",\r\n    \"trendType\": \"1\"\r\n  },\r\n  \"option\": {\r\n    \"isCompare\": false,\r\n    \"trendType\": \"1\",\r\n    \"card\": {\r\n      \"title\": \"未命名标题\",\r\n      \"extra\": \"\",\r\n      \"rightHref\": \"\",\r\n      \"size\": \"small\",\r\n      \"textStyle\": {\r\n        \"fontSize\": 18,\r\n        \"fontWeight\": \"bold\",\r\n        \"color\": \"#464646\"\r\n      }\r\n    },\r\n    \"body\": {\r\n      \"text\": \"\",\r\n      \"color\": \"#000000\",\r\n      \"fontWeight\": \"bold\"\r\n    }\r\n  }\r\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-09-01 15:10:03', `update_by` = 'jeecg', `update_time` = '2022-09-02 16:52:23' WHERE `id` = '725214423934730240';

-- 修改基础条形图配置项
UPDATE `onl_drag_comp` SET `parent_id` = '200200', `comp_name` = '基础条形图', `comp_type` = 'JHorizontalBar', `icon` = 'ic:baseline-bar-chart', `order_num` = NULL, `type_id` = NULL, `comp_config` = '{\r\n  \"w\": 12,\r\n  \"h\": 30,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/chart\",\n  \"timeOut\": 0,\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"dataMapping\": [\n    {\n      \"filed\": \"维度\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"chartData\": [\n    {\n      \"name\": \"苹果\",\n      \"value\": 1000879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"三星\",\n      \"value\": 3400879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"小米\",\n      \"value\": 2300879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"oppo\",\n      \"value\": 5400879,\n      \"type\": \"手机品牌\"\n    },\n    {\n      \"name\": \"vivo\",\n      \"value\": 3400879,\n      \"type\": \"手机品牌\"\n    }\n  ],\n  \"option\": {\n    \"grid\": {\n      \"show\": false,\n      \"top\":40, \n      \"bottom\": 40,\r\n			\"containLabel\": true\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"基础条形图\",\n      \"show\": true,\n      \"textStyle\": {\n        \"color\": \"#464646\",\r\n				\"fontWeight\":\"normal\"\n      }\n    },\n    \"tooltip\": {\n      \"trigger\": \"axis\",\n      \"axisPointer\": {\n        \"type\": \"shadow\",\n        \"label\": {\n          \"show\": true,\n          \"backgroundColor\": \"#333\"\n        }\n      }\n    },\n    \"yAxis\": {\r\n		  \"type\": \"category\",\n      \"splitLine\": {\n        \"interval\": 2\n      },\n      \"lineStyle\": {\n        \"color\": \"#f3f3f3\"\n      }\n    },\n    \"series\": [\n      {\n        \"data\": [],\n        \"type\": \"bar\",\n        \"barWidth\": 20,\n        \"itemStyle\": {\n          \"color\": \"#64b5f6\",\n          \"borderRadius\": 0\n        }\n      }\n    ]\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-04 19:18:21' WHERE `id` = '200200208';

-- 修改地图的缩放默认配置
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '散点地图', `comp_type` = 'JBubbleMap', `icon` = 'ic:outline-scatter-plot', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"activeKey\": 1,\n  \"dataType\": 1,\n  \"background\": \"#ffffff\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n       \"dataMapping\": [{\n        \"filed\": \"区域\",\n        \"mapping\": \"\"\n    }, {\n        \"filed\": \"数值\",\n        \"mapping\": \"\"\n    }],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"jsConfig\": \"\",\n  \"chartData\": [\n    {\n      \"name\": \"北京\",\n      \"value\": 199\n    },\n    {\n      \"name\": \"天津\",\n      \"value\": 42\n    },\n    {\n      \"name\": \"河北\",\n      \"value\": 102\n    },\n    {\n      \"name\": \"山西\",\n      \"value\": 81\n    },\n    {\n      \"name\": \"内蒙古\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"辽宁\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"吉林\",\n      \"value\": 82\n    },\n    {\n      \"name\": \"黑龙江\",\n      \"value\": 123\n    },\n    {\n      \"name\": \"上海\",\n      \"value\": 24\n    },\n    {\n      \"name\": \"江苏\",\n      \"value\": 92\n    },\n    {\n      \"name\": \"浙江\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"安徽\",\n      \"value\": 109\n    },\n    {\n      \"name\": \"福建\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"江西\",\n      \"value\": 91\n    },\n    {\n      \"name\": \"山东\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"河南\",\n      \"value\": 137\n    },\n    {\n      \"name\": \"湖北\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"湖南\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"重庆\",\n      \"value\": 91\n    },\n    {\n      \"name\": \"四川\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"贵州\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"云南\",\n      \"value\": 83\n    },\n    {\n      \"name\": \"西藏\",\n      \"value\": 9\n    },\n    {\n      \"name\": \"陕西\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"甘肃\",\n      \"value\": 56\n    },\n    {\n      \"name\": \"青海\",\n      \"value\": 10\n    },\n    {\n      \"name\": \"宁夏\",\n      \"value\": 18\n    },\n    {\n      \"name\": \"新疆\",\n      \"value\": 180\n    },\n    {\n      \"name\": \"广东\",\n      \"value\": 123\n    },\n    {\n      \"name\": \"广西\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"海南\",\n      \"value\": 14\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#fff176\",\n    \"barColor2\": \"#fcc02e\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#fcc02e\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#000000\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"left\": 10,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 200,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        1\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": false,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#fff59c\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 10:32:09' WHERE `id` = '100120100';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '飞线地图', `comp_type` = 'JFlyLineMap', `icon` = 'la:plane', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"dataType\": 1,\n  \"background\": \"#292626\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"贵州\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 106.6992,\n      \"toLat\": 26.7682,\n      \"value\": 100\n    },\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"北京\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 116.46,\n      \"toLat\": 39.92,\n      \"value\": 100\n    },\n    {\n      \"fromName\": \"新疆\",\n      \"toName\": \"北京\",\n      \"fromLng\": 87.68,\n      \"fromLat\": 43.67,\n      \"toLng\": 116.46,\n      \"toLat\": 39.92,\n      \"value\": 100\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#323c48\",\n      \"color2\": \"#3B373700\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"effect\": {\n      \"show\": true,\n      \"trailLength\": 0,\n      \"period\": 6,\n      \"symbolSize\": 15\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"padding\": [\n        5,\n        0,\n        0,\n        15\n      ],\n      \"show\": true\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 200,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        2\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": false,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#5A7864\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"#323c48\",\n          \"shadowColor\": \"\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#EEDD78\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:16:54' WHERE `id` = '100120101';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '柱形地图', `comp_type` = 'JBarMap', `icon` = 'uil:graph-bar', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"dataType\": 1,\n  \"background\": \"#ffffff\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"name\": \"北京\",\n      \"value\": 900\n    },\n    {\n      \"name\": \"山西\",\n      \"value\": 1681\n    },\n    {\n      \"name\": \"内蒙古\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"辽宁\",\n      \"value\": 1667\n    },\n    {\n      \"name\": \"福建\",\n      \"value\": 516\n    },\n    {\n      \"name\": \"江西\",\n      \"value\": 591\n    },\n    {\n      \"name\": \"山东\",\n      \"value\": 419\n    },\n    {\n      \"name\": \"河南\",\n      \"value\": 137\n    },\n    {\n      \"name\": \"云南\",\n      \"value\": 983\n    },\n    {\n      \"name\": \"西藏\",\n      \"value\": 9\n    },\n    {\n      \"name\": \"陕西\",\n      \"value\": 580\n    },\n    {\n      \"name\": \"甘肃\",\n      \"value\": 556\n    },\n    {\n      \"name\": \"海南\",\n      \"value\": 14\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 12,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#3B373700\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#000000\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"tooltip\": {\n      \"trigger\": \"item\",\n      \"show\": false,\n      \"enterable\": true,\n      \"textStyle\": {\n        \"fontSize\": 20,\n        \"color\": \"#fff\"\n      },\n      \"backgroundColor\": \"rgba(0,2,89,0.8)\"\n    },\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"left\": 10,\n      \"show\": true\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"max\": 200,\n      \"seriesIndex\": [\n        0\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"roam\": false,\n      \"aspectScale\": 0.96,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"#37805B\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\r\n        \"emphasis\": {\r\n          \"areaColor\": \"#fff59c\"\r\n        }\n      }\n    },\n    \"series\": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:01' WHERE `id` = '100120102';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '时间轴飞线地图', `comp_type` = 'JTotalFlyLineMap', `icon` = 'fluent:airplane-take-off-16-regular', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 55,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n  \"background\": \"#000000\",\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"贵州\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 106.6992,\n      \"toLat\": 26.7682,\n      \"value\": 100,\n      \"group\": 2017\n    },\n    {\n      \"fromName\": \"河南\",\n      \"toName\": \"云南\",\n      \"fromLng\": 113.4668,\n      \"fromLat\": 34.6234,\n      \"toLng\": 102.9199,\n      \"toLat\": 25.4663,\n      \"value\": 100,\n      \"group\": 2017\n    },\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"甘肃\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 103.5901,\n      \"toLat\": 36.3043,\n      \"value\": 100,\n      \"group\": 2018\n    },\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"广东\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 113.12244,\n      \"toLat\": 31.9208,\n      \"value\": 147,\n      \"group\": 2018\n    },\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"北京\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 116.4551,\n      \"toLat\": 40.2539,\n      \"value\": 100,\n      \"group\": 2019\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": true,\n    \"areaColor\": {\n      \"color1\": \"#0A0909\",\n      \"color2\": \"#3B373700\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"show\": true,\n      \"left\": 10,\n      \"textStyle\": {\r\n			  \"fontWeight\":\"normal\",\n        \"color\": \"#70DB93\",\n        \"fontSize\": \"22px\"\n      },\n      \"subtextStyle\": {\n        \"color\": \"#ffffff\",\n        \"fontSize\": \"12px\"\n      }\n    },\n    \"geo\": {\n      \"top\": 50,\n      \"left\": 100,\n      \"label\": {\n        \"normal\": {\n          \"show\": false\n        },\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": false,\n      \"zoom\": 0.9,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#93ebf8\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": -2,\n          \"shadowOffsetY\": 2,\n          \"shadowBlur\": 10\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#EEDD78\",\n          \"borderWidth\": 0\n        }\n      }\n    },\n    \"timeline\": {\n      \"show\": true,\n      \"axisType\": \"category\",\n      \"autoPlay\": false,\n      \"playInterval\": 2000,\n      \"left\": \"10%\",\n      \"right\": \"5%\",\n      \"bottom\": 10,\n      \"padding\": 5,\n      \"width\": \"80%\",\n      \"label\": {\n        \"normal\": {\n          \"textStyle\": {\n            \"color\": \"#ffffff\"\n          }\n        },\n        \"emphasis\": {\n          \"textStyle\": {\n            \"color\": \"#000000\"\n          }\n        }\n      },\n      \"symbolSize\": 10,\n      \"lineStyle\": {\n        \"color\": \"#555555\"\n      },\n      \"checkpointStyle\": {\n        \"borderColor\": \"#777777\",\n        \"borderWidth\": 2\n      },\n      \"controlStyle\": {\n        \"showNextBtn\": true,\n        \"showPrevBtn\": true,\n        \"normal\": {\n          \"color\": \"#666666\",\n          \"borderColor\": \"#666666\"\n        },\n        \"emphasis\": {\n          \"color\": \"#aaaaaa\",\n          \"borderColor\": \"#aaaaaa\"\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:09' WHERE `id` = '100120103';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '柱形排名形地图', `comp_type` = 'JTotalBarMap', `icon` = 'ph:chart-bar-horizontal', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 55,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n  \"background\": \"#000000\",\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"name\": \"江苏\",\n      \"lng\": 118.8062,\n      \"lat\": 31.9208,\n      \"value\": 500,\n      \"group\": 2017\n    },\n    {\n      \"name\": \"贵州\",\n      \"lng\": 106.6992,\n      \"lat\": 26.7682,\n      \"value\": 100,\n      \"group\": 2017\n    },\n    {\n      \"name\": \"河南\",\n      \"lng\": 113.4668,\n      \"lat\": 34.6234,\n      \"value\": 100,\n      \"group\": 2017\n    },\n    {\n      \"name\": \"云南\",\n      \"lng\": 102.9199,\n      \"lat\": 25.4663,\n      \"value\": 300,\n      \"group\": 2017\n    },\n    {\n      \"name\": \"江苏\",\n      \"lng\": 118.8062,\n      \"lat\": 31.9208,\n      \"value\": 478,\n      \"group\": 2018\n    },\n    {\n      \"name\": \"贵州\",\n      \"lng\": 106.6992,\n      \"lat\": 26.7682,\n      \"value\": 269,\n      \"group\": 2018\n    },\n    {\n      \"name\": \"河南\",\n      \"lng\": 113.4668,\n      \"lat\": 34.6234,\n      \"value\": 128,\n      \"group\": 2018\n    },\n    {\n      \"name\": \"云南\",\n      \"lng\": 102.9199,\n      \"lat\": 25.4663,\n      \"value\": 100,\n      \"group\": 2018\n    },\n    {\n      \"name\": \"江苏\",\n      \"lng\": 118.8062,\n      \"lat\": 31.9208,\n      \"value\": 236,\n      \"group\": 2019\n    },\n    {\n      \"name\": \"贵州\",\n      \"lng\": 106.6992,\n      \"lat\": 26.7682,\n      \"value\": 569,\n      \"group\": 2019\n    },\n    {\n      \"name\": \"河南\",\n      \"lng\": 113.4668,\n      \"lat\": 34.6234,\n      \"value\": 479,\n      \"group\": 2019\n    },\n    {\n      \"name\": \"云南\",\n      \"lng\": 102.9199,\n      \"lat\": 25.4663,\n      \"value\": 259,\n      \"group\": 2019\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": true,\n    \"mapTitle\": \"\",\n    \"dataTitle\": \"数据统计情况\",\n    \"dataTitleSize\": 20,\n    \"dataTitleColor\": \"#ffffff\",\n    \"dataNameColor\": \"#dddddd\",\n    \"dataValueColor\": \"#dddddd\",\n    \"areaColor\": {\n      \"color1\": \"#0A0909\",\n      \"color2\": \"#3B373700\"\n    },\n    \"grid\": {\n      \"bottom\": 50,\n      \"left\": 75,\n      \"top\": 20\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"综合统计地图\",\n      \"show\": true,\n      \"left\": 10,\n      \"textStyle\": {\n        \"color\": \"#ffffff\",\n        \"fontSize\": \"22px\"\n      },\n      \"subtextStyle\": {\n        \"color\": \"#ffffff\",\n        \"fontSize\": \"12px\"\n      }\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"radar\": [\n      {\n        \"indicator\": []\n      }\n    ],\n    \"timeline\": {\n      \"show\": true,\n      \"axisType\": \"category\",\n      \"autoPlay\": false,\n      \"playInterval\": 2000,\n      \"left\": \"10%\",\n      \"right\": \"5%\",\n      \"bottom\": 5,\n      \"padding\": 5,\n      \"width\": \"80%\",\n      \"label\": {\n        \"normal\": {\n          \"textStyle\": {\n            \"color\": \"#ffffff\"\n          }\n        },\n        \"emphasis\": {\n          \"textStyle\": {\n            \"color\": \"#000000\"\n          }\n        }\n      },\n      \"symbolSize\": 10,\n      \"lineStyle\": {\n        \"color\": \"#555555\"\n      },\n      \"checkpointStyle\": {\n        \"borderColor\": \"#777777\",\n        \"borderWidth\": 2\n      },\n      \"controlStyle\": {\n        \"showNextBtn\": true,\n        \"showPrevBtn\": true,\n        \"normal\": {\n          \"color\": \"#666666\",\n          \"borderColor\": \"#666666\"\n        },\n        \"emphasis\": {\n          \"color\": \"#aaaaaa\",\n          \"borderColor\": \"#aaaaaa\"\n        }\n      }\n    },\n    \"geo\": {\n      \"top\": 80,\n      \"left\": \"3%\",\n      \"show\": true,\n      \"roam\": false,\n      \"zoom\": 0.9,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false\n        }\n      },\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#93ebf8\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": -2,\n          \"shadowOffsetY\": 2,\n          \"shadowBlur\": 10\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#EEDD78\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:17' WHERE `id` = '100120105';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '热力地图', `comp_type` = 'JHeatMap', `icon` = 'carbon:heat-map-02', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"dataType\": 1,\n  \"background\": \"#000000\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"name\": \"海门\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"鄂尔多斯\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"招远\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"舟山\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"齐齐哈尔\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"盐城\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"赤峰\",\n      \"value\": 16\n    },\n    {\n      \"name\": \"青岛\",\n      \"value\": 450\n    },\n    {\n      \"name\": \"乳山\",\n      \"value\": 118\n    },\n    {\n      \"name\": \"金昌\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"泉州\",\n      \"value\": 21\n    },\n    {\n      \"name\": \"莱西\",\n      \"value\": 300\n    },\n    {\n      \"name\": \"日照\",\n      \"value\": 121\n    },\n    {\n      \"name\": \"胶南\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"南通\",\n      \"value\": 23\n    },\n    {\n      \"name\": \"拉萨\",\n      \"value\": 321\n    },\n    {\n      \"name\": \"云浮\",\n      \"value\": 444\n    },\n    {\n      \"name\": \"梅州\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"文登\",\n      \"value\": 456\n    },\n    {\n      \"name\": \"上海\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"攀枝花\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"威海\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"承德\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"厦门\",\n      \"value\": 126\n    },\n    {\n      \"name\": \"汕尾\",\n      \"value\": 26\n    },\n    {\n      \"name\": \"潮州\",\n      \"value\": 247\n    },\n    {\n      \"name\": \"丹东\",\n      \"value\": 227\n    },\n    {\n      \"name\": \"太仓\",\n      \"value\": 427\n    },\n    {\n      \"name\": \"曲靖\",\n      \"value\": 327\n    },\n    {\n      \"name\": \"烟台\",\n      \"value\": 28\n    },\n    {\n      \"name\": \"福州\",\n      \"value\": 29\n    },\n    {\n      \"name\": \"瓦房店\",\n      \"value\": 30\n    },\n    {\n      \"name\": \"即墨\",\n      \"value\": 30\n    },\n    {\n      \"name\": \"抚顺\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"玉溪\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"张家口\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"阳泉\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"莱州\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"湖州\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"汕头\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"昆山\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"宁波\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"湛江\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"揭阳\",\n      \"value\": 34\n    },\n    {\n      \"name\": \"荣成\",\n      \"value\": 34\n    },\n    {\n      \"name\": \"连云港\",\n      \"value\": 35\n    },\n    {\n      \"name\": \"葫芦岛\",\n      \"value\": 35\n    },\n    {\n      \"name\": \"常熟\",\n      \"value\": 236\n    },\n    {\n      \"name\": \"东莞\",\n      \"value\": 336\n    },\n    {\n      \"name\": \"河源\",\n      \"value\": 36\n    },\n    {\n      \"name\": \"淮安\",\n      \"value\": 436\n    },\n    {\n      \"name\": \"泰州\",\n      \"value\": 236\n    },\n    {\n      \"name\": \"南宁\",\n      \"value\": 437\n    },\n    {\n      \"name\": \"营口\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"惠州\",\n      \"value\": 337\n    },\n    {\n      \"name\": \"江阴\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"蓬莱\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"韶关\",\n      \"value\": 38\n    },\n    {\n      \"name\": \"嘉峪关\",\n      \"value\": 38\n    },\n    {\n      \"name\": \"广州\",\n      \"value\": 138\n    },\n    {\n      \"name\": \"延安\",\n      \"value\": 138\n    },\n    {\n      \"name\": \"太原\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"清远\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"中山\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"昆明\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"寿光\",\n      \"value\": 440\n    },\n    {\n      \"name\": \"盘锦\",\n      \"value\": 40\n    },\n    {\n      \"name\": \"长治\",\n      \"value\": 41\n    },\n    {\n      \"name\": \"深圳\",\n      \"value\": 41\n    },\n    {\n      \"name\": \"珠海\",\n      \"value\": 42\n    },\n    {\n      \"name\": \"宿迁\",\n      \"value\": 43\n    },\n    {\n      \"name\": \"咸阳\",\n      \"value\": 43\n    },\n    {\n      \"name\": \"铜川\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"平度\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"佛山\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"海口\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"江门\",\n      \"value\": 45\n    },\n    {\n      \"name\": \"章丘\",\n      \"value\": 45\n    },\n    {\n      \"name\": \"肇庆\",\n      \"value\": 46\n    },\n    {\n      \"name\": \"大连\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"临汾\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"吴江\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"石嘴山\",\n      \"value\": 49\n    },\n    {\n      \"name\": \"沈阳\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"苏州\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"茂名\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"嘉兴\",\n      \"value\": 51\n    },\n    {\n      \"name\": \"长春\",\n      \"value\": 51\n    },\n    {\n      \"name\": \"胶州\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"银川\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"张家港\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"三门峡\",\n      \"value\": 53\n    },\n    {\n      \"name\": \"锦州\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"南昌\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"柳州\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"三亚\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"自贡\",\n      \"value\": 156\n    },\n    {\n      \"name\": \"吉林\",\n      \"value\": 156\n    },\n    {\n      \"name\": \"阳江\",\n      \"value\": 257\n    },\n    {\n      \"name\": \"泸州\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"西宁\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"宜宾\",\n      \"value\": 258\n    },\n    {\n      \"name\": \"呼和浩特\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"成都\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"大同\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"镇江\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"桂林\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"张家界\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"宜兴\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"北海\",\n      \"value\": 60\n    },\n    {\n      \"name\": \"西安\",\n      \"value\": 61\n    },\n    {\n      \"name\": \"金坛\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"东营\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"牡丹江\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"遵义\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"绍兴\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"扬州\",\n      \"value\": 64\n    },\n    {\n      \"name\": \"常州\",\n      \"value\": 64\n    },\n    {\n      \"name\": \"潍坊\",\n      \"value\": 65\n    },\n    {\n      \"name\": \"重庆\",\n      \"value\": 66\n    },\n    {\n      \"name\": \"台州\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"南京\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"滨州\",\n      \"value\": 70\n    },\n    {\n      \"name\": \"贵阳\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"无锡\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"本溪\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"克拉玛依\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"渭南\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"马鞍山\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"宝鸡\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"焦作\",\n      \"value\": 75\n    },\n    {\n      \"name\": \"句容\",\n      \"value\": 75\n    },\n    {\n      \"name\": \"北京\",\n      \"value\": 79\n    },\n    {\n      \"name\": \"徐州\",\n      \"value\": 79\n    },\n    {\n      \"name\": \"衡水\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"包头\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"绵阳\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"乌鲁木齐\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"枣庄\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"杭州\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"淄博\",\n      \"value\": 85\n    },\n    {\n      \"name\": \"鞍山\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"溧阳\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"库尔勒\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"安阳\",\n      \"value\": 190\n    },\n    {\n      \"name\": \"开封\",\n      \"value\": 390\n    },\n    {\n      \"name\": \"济南\",\n      \"value\": 292\n    },\n    {\n      \"name\": \"德阳\",\n      \"value\": 393\n    },\n    {\n      \"name\": \"温州\",\n      \"value\": 95\n    },\n    {\n      \"name\": \"九江\",\n      \"value\": 96\n    },\n    {\n      \"name\": \"邯郸\",\n      \"value\": 98\n    },\n    {\n      \"name\": \"临安\",\n      \"value\": 99\n    },\n    {\n      \"name\": \"兰州\",\n      \"value\": 99\n    },\n    {\n      \"name\": \"沧州\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"临沂\",\n      \"value\": 103\n    },\n    {\n      \"name\": \"南充\",\n      \"value\": 104\n    },\n    {\n      \"name\": \"天津\",\n      \"value\": 105\n    },\n    {\n      \"name\": \"富阳\",\n      \"value\": 106\n    },\n    {\n      \"name\": \"泰安\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"诸暨\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"郑州\",\n      \"value\": 113\n    },\n    {\n      \"name\": \"哈尔滨\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"聊城\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"芜湖\",\n      \"value\": 117\n    },\n    {\n      \"name\": \"唐山\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"平顶山\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"邢台\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"德州\",\n      \"value\": 120\n    },\n    {\n      \"name\": \"济宁\",\n      \"value\": 120\n    },\n    {\n      \"name\": \"荆州\",\n      \"value\": 127\n    },\n    {\n      \"name\": \"宜昌\",\n      \"value\": 130\n    },\n    {\n      \"name\": \"义乌\",\n      \"value\": 132\n    },\n    {\n      \"name\": \"丽水\",\n      \"value\": 133\n    },\n    {\n      \"name\": \"洛阳\",\n      \"value\": 134\n    },\n    {\n      \"name\": \"秦皇岛\",\n      \"value\": 136\n    },\n    {\n      \"name\": \"株洲\",\n      \"value\": 143\n    },\n    {\n      \"name\": \"石家庄\",\n      \"value\": 147\n    },\n    {\n      \"name\": \"莱芜\",\n      \"value\": 148\n    },\n    {\n      \"name\": \"常德\",\n      \"value\": 152\n    },\n    {\n      \"name\": \"保定\",\n      \"value\": 153\n    },\n    {\n      \"name\": \"湘潭\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"金华\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"岳阳\",\n      \"value\": 169\n    },\n    {\n      \"name\": \"长沙\",\n      \"value\": 175\n    },\n    {\n      \"name\": \"衢州\",\n      \"value\": 177\n    },\n    {\n      \"name\": \"廊坊\",\n      \"value\": 193\n    },\n    {\n      \"name\": \"菏泽\",\n      \"value\": 194\n    },\n    {\n      \"name\": \"合肥\",\n      \"value\": 229\n    },\n    {\n      \"name\": \"武汉\",\n      \"value\": 273\n    },\n    {\n      \"name\": \"大庆\",\n      \"value\": 279\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#3B373700\"\n    },\n    \"heat\": {\n      \"pointSize\": 15,\n      \"blurSize\": 20,\n      \"maxOpacity\": 1\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#E08D8D\",\n        \"#ff9800\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#df2425\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"left\": 10,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"visualMap\": {\n      \"show\": true,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 0,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        1\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": false,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#fff59c\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:24' WHERE `id` = '100120106';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '区域地图', `comp_type` = 'JAreaMap', `icon` = 'ic:outline-scatter-plot', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"activeKey\": 1,\n  \"dataType\": 1,\n  \"background\": \"#ffffff\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n       \"dataMapping\": [{\n        \"filed\": \"区域\",\n        \"mapping\": \"\"\n    }, {\n        \"filed\": \"数值\",\n        \"mapping\": \"\"\n    }],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"jsConfig\": \"\",\n  \"chartData\": [\n    {\n      \"name\": \"北京\",\n      \"value\": 199\n    },\n    {\n      \"name\": \"天津\",\n      \"value\": 42\n    },\n    {\n      \"name\": \"河北\",\n      \"value\": 102\n    },\n    {\n      \"name\": \"山西\",\n      \"value\": 81\n    },\n    {\n      \"name\": \"内蒙古\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"辽宁\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"吉林\",\n      \"value\": 82\n    },\n    {\n      \"name\": \"黑龙江\",\n      \"value\": 123\n    },\n    {\n      \"name\": \"上海\",\n      \"value\": 24\n    },\n    {\n      \"name\": \"江苏\",\n      \"value\": 92\n    },\n    {\n      \"name\": \"浙江\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"安徽\",\n      \"value\": 109\n    },\n    {\n      \"name\": \"福建\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"江西\",\n      \"value\": 91\n    },\n    {\n      \"name\": \"山东\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"河南\",\n      \"value\": 137\n    },\n    {\n      \"name\": \"湖北\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"湖南\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"重庆\",\n      \"value\": 91\n    },\n    {\n      \"name\": \"四川\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"贵州\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"云南\",\n      \"value\": 83\n    },\n    {\n      \"name\": \"西藏\",\n      \"value\": 9\n    },\n    {\n      \"name\": \"陕西\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"甘肃\",\n      \"value\": 56\n    },\n    {\n      \"name\": \"青海\",\n      \"value\": 10\n    },\n    {\n      \"name\": \"宁夏\",\n      \"value\": 18\n    },\n    {\n      \"name\": \"新疆\",\n      \"value\": 180\n    },\n    {\n      \"name\": \"广东\",\n      \"value\": 123\n    },\n    {\n      \"name\": \"广西\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"海南\",\n      \"value\": 14\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#fff176\",\n    \"barColor2\": \"#fcc02e\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#fcc02e\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#000000\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"left\": 10,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 200,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        0\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": false,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#fff59c\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 10:32:09' WHERE `id` = '100120107';

-- 修改当前时间组件的默认宽度
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '当前时间', `comp_type` = 'JCurrentTime', `icon` = 'ant-design:field-time-outlined', `order_num` = 100, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 6,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/42/nav\",\n  \"timeOut\": 0,\n	\"background\": \"#3F7DD4\",\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"chartData\": \"\",\n  \"option\": {\r\n	  \"showWeek\":\"show\",\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"body\": {\n      \"text\": \"\",\n      \"color\": \"#FFFFFF\",\n      \"fontWeight\": \"normal\",\n      \"marginLeft\": 0,\n      \"marginTop\": 0\n    }\n  }\n}', `status` = '1', `create_by` = 'jeecg', `create_time` = '2024-03-25 20:32:51', `update_by` = 'jeecg', `update_time` = '2024-03-25 20:34:14' WHERE `id` = '932219134883299328';

-- 修改顶级组件的名称
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '常用', `comp_type` = '', `icon` = 'ant-design:setting-twotone', `order_num` = 2, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'jeecg', `update_time` = '2022-04-24 11:02:19' WHERE `id` = '100';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '布局', `comp_type` = 'layout', `icon` = 'ic:baseline-tab', `order_num` = 3, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 09:49:13' WHERE `id` = '100101';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '数据', `comp_type` = 'dataList', `icon` = 'ant-design:table-outlined', `order_num` = 3, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 09:49:48' WHERE `id` = '100102';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '首页', `comp_type` = 'home', `icon` = 'carbon:home', `order_num` = 9, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 09:50:07' WHERE `id` = '100104';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '地图', `comp_type` = 'dataList', `icon` = 'ant-design:table-outlined', `order_num` = 3, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 09:49:48' WHERE `id` = '100120';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '图表', `comp_type` = 'chart', `icon` = 'ant-design:bar-chart-outlined', `order_num` = 1, `type_id` = NULL, `comp_config` = '', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-04-29 17:52:06' WHERE `id` = '200';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '自定义', `comp_type` = 'customForm', `icon` = 'ant-design:project-filled', `order_num` = 2, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-07-13 19:02:51', `update_by` = 'ldd', `update_time` = '2023-02-17 21:18:37' WHERE `id` = '707153616621699072';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '个性', `comp_type` = 'custom', `icon` = 'ant-design:appstore-twotone', `order_num` = 100, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = 'admin', `create_time` = '2022-07-18 19:22:09', `update_by` = 'admin', `update_time` = '2022-07-18 19:33:20' WHERE `id` = '708970414976712704';