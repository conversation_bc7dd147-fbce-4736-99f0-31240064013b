package ${bussiPackage}.${entityPackage}.vo;

import java.util.List;
import ${bussiPackage}.${entityPackage}.entity.${entityName};
<#list subTables as sub>
import ${bussiPackage}.${entityPackage}.entity.${sub.entityName};
</#list>
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelEntity;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Data
@ApiModel(value="${tableName}Page对象", description="${tableVo.ftlDescription}")
public class ${entityName}Page {

	<#assign excel_ignore_arr=['createBy','createTime','updateBy','updateTime','sysOrgCode']>
	<#assign excel_ignore_classType_arr=['pca','switch','cat_tree']>
    <#list originalColumns as po>
    <#-- 生成字典Code -->
    <#assign list_field_dictCode="">
    <#if po.classType='sel_user'>
      <#assign list_field_dictCode=', dictTable = "sys_user", dicText = "realname", dicCode = "username"'>
    <#elseif po.classType='sel_depart'>
      <#assign list_field_dictCode=', dictTable = "sys_depart", dicText = "depart_name", dicCode = "id"'>
    <#elseif po.classType=='list' || po.classType=='list_multi' || po.classType=='sel_search' || po.classType=='radio' || po.classType=='checkbox'>
      <#if po.dictTable?default("")?trim?length gt 1>
        <#assign list_field_dictCode=', dictTable = "${po.dictTable}", dicText = "${po.dictText}", dicCode = "${po.dictField}"'>
      <#elseif po.dictField?default("")?trim?length gt 1>
        <#assign list_field_dictCode=', dicCode = "${po.dictField}"'>
      </#if>
      <#elseif po.classType=='cat_tree'>
          <#assign list_field_dictCode=', dictTable = "sys_category", dicText = "name", dicCode = "id"'>
    </#if>
	/**${po.filedComment}*/
	<#if po.fieldName == primaryKeyField>
	<#else>
		<#if po.fieldDbType =='Date' || po.fieldDbType =='Datetime'>
			<#if po.classType=='date'>
	<#if !excel_ignore_arr?seq_contains("${po.fieldName}")>
	@Excel(name = "${po.filedComment}", width = 15, format = "yyyy-MM-dd")
	</#if>
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
			<#else>
	<#if !excel_ignore_arr?seq_contains("${po.fieldName}")>
	@Excel(name = "${po.filedComment}", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	</#if>
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
			</#if>
		<#else>
	<#if !excel_ignore_arr?seq_contains("${po.fieldName}")  && !excel_ignore_classType_arr?seq_contains("${po.classType}")>
	@Excel(name = "${po.filedComment}", width = 15${list_field_dictCode})
	</#if>
		</#if>
        <#if list_field_dictCode?length gt 1  && !excel_ignore_classType_arr?seq_contains("${po.classType}")>
    @Dict(${list_field_dictCode?substring(2)})
        </#if>
	</#if>
	@ApiModelProperty(value = "${po.filedComment}")
    <#if po.fieldDbType=='Blob'>
    private java.lang.String ${po.fieldName}String;
    <#elseif po.classType=='pca'>
    @Excel(name = "${po.filedComment}", width = 15,exportConvert=true,importConvert = true )
    private ${po.fieldType} ${po.fieldName};

    public String convertis${po.fieldName?cap_first}() {
        return SpringContextUtils.getBean(ProvinceCityArea.class).getText(${po.fieldName});
    }

    public void convertset${po.fieldName?cap_first}(String text) {
        this.${po.fieldName} = SpringContextUtils.getBean(ProvinceCityArea.class).getCode(text);
    }
    <#elseif po.classType=='cat_tree'>
    @Excel(name = "${po.filedComment}", width = 15${list_field_dictCode})
    private ${po.fieldType} ${po.fieldName};
    <#elseif po.classType=='switch'>
    <#assign switch_extend_arr=['Y','N']>
    <#if po.dictField?default("")?contains("[")>
        <#assign switch_extend_arr=po.dictField?eval>
    </#if>
    <#list switch_extend_arr as a>
        <#if a_index == 0>
            <#assign switch_extend_arr1=a>
        <#else>
            <#assign switch_extend_arr2=a>
        </#if>
    </#list>
    @Excel(name = "${po.filedComment}", width = 15,replace = {"是_${switch_extend_arr1}","否_${switch_extend_arr2}"} )
    private ${po.fieldType} ${po.fieldName};
    <#else>
    private ${po.fieldType} ${po.fieldName};
    </#if>
	</#list>

	<#list subTables as sub>
	@ExcelCollection(name="${sub.ftlDescription}")
	@ApiModelProperty(value = "${sub.ftlDescription}")
	private List<${sub.entityName}> ${sub.entityName?uncap_first}List;
	</#list>

}
