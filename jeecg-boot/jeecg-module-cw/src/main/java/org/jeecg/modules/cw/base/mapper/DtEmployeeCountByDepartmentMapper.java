package org.jeecg.modules.cw.base.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.cw.base.entity.DtEmployeeCountByDepartment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Date;

/**
 * Mapper for 德铜人员统计表 dt_employee_count_by_department
 */
public interface DtEmployeeCountByDepartmentMapper extends BaseMapper<DtEmployeeCountByDepartment> {

    /**
     * 统计指定时间范围内的人员总和
     *
     * @param startDate 开始日期（含）
     * @param endDate   结束日期（含）
     * @return 人员总数，若无记录返回 0
     */
    Integer totalEmployeeCount(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取指定月份最后一天的人员总和（排除特定单位）
     *
     * @param startDate 月份开始日期
     * @param endDate   月份结束日期
     * @return 过滤后的人员总数，若无记录返回 0
     */
    Integer getFilteredEmployeeCountForMonth(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
} 