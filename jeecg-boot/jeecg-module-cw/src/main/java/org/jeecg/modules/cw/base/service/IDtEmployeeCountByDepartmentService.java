package org.jeecg.modules.cw.base.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.cw.base.entity.DtEmployeeCountByDepartment;

import java.util.Date;

/**
 * Service interface for 德铜人员统计表
 */
public interface IDtEmployeeCountByDepartmentService extends IService<DtEmployeeCountByDepartment> {

    /**
     * 获取指定时间范围内的人员总数
     *
     * @param startDate 开始日期（含）
     * @param endDate   结束日期（含）
     * @return 总人数
     */
    Integer getTotalEmployeeCount(Date startDate, Date endDate);

    /**
     * 获取指定月份的人员总数（排除特定单位：新技术、铸造、实业、化工、铜兴监测）
     * 取当月最后一天的人数总和
     *
     * @param startDate 月份开始日期
     * @param endDate   月份结束日期
     * @return 过滤后的人员总数
     */
    Integer getFilteredEmployeeCountForMonth(Date startDate, Date endDate);
} 