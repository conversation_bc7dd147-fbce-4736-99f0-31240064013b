package org.jeecg.modules.cw.quartz;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.cw.ckc.entity.CwCkcDay;
import org.jeecg.modules.cw.ckc.service.ICwCkcDayService;
import org.jeecg.modules.cw.ds.entity.CwDsDay;
import org.jeecg.modules.cw.ds.service.ICwDsDayService;
import org.jeecg.modules.cw.frdw.entity.CwFrdwDay;
import org.jeecg.modules.cw.frdw.service.ICwFrdwDayService;
import org.jeecg.modules.cw.frdw.service.ICwFrdwService;
import org.jeecg.modules.cw.jg.entity.CwJgDay;
import org.jeecg.modules.cw.jg.service.ICwJgDayService;
import org.jeecg.modules.cw.jh.entity.CwJhDay;
import org.jeecg.modules.cw.jh.service.ICwJhDayService;
import org.jeecg.modules.cw.jw.entity.CwJwDay;
import org.jeecg.modules.cw.jw.service.ICwJwDayService;
import org.jeecg.modules.cw.mnlr.entity.CwMnlrDay;
import org.jeecg.modules.cw.mnlr.service.ICwMnlrDayService;
import org.jeecg.modules.cw.qtfy.entity.CwQtfy;
import org.jeecg.modules.cw.qtfy.service.ICwQtfyService;
import org.jeecg.modules.cw.sx.entity.CwSxDay;
import org.jeecg.modules.cw.sx.service.ICwSxDayService;
import org.jeecg.modules.cw.xjs.entity.CwXjsDay;
import org.jeecg.modules.cw.xjs.service.ICwXjsDayService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class CopyTableJob implements Job {

    @Resource
    private ICwCkcDayService ckcDayService;
    @Resource
    private ICwDsDayService dsDayService;
    @Resource
    private ICwSxDayService sxDayService;
    @Resource
    private ICwJwDayService jwDayService;
    @Resource
    private ICwXjsDayService xjsDayService;
    @Resource
    private ICwJhDayService jhDayService;
    @Resource
    private ICwJgDayService jgDayService;
    @Resource
    private ICwQtfyService qtfyService;
    @Resource
    private ICwMnlrDayService mnlrDayService;
    @Resource
    private ICwFrdwDayService frdwService;

    @Override
    public void execute(JobExecutionContext ctx) throws JobExecutionException {
        // 从Job参数中获取参数
        String parameter = (String) ctx.getJobDetail().getJobDataMap().get("parameter");
        JSONObject paramJson = JSONObject.parseObject(parameter);

        List<String> tablesToCopy = null;
        List<String> dateList = null;
        if (ObjectUtil.isNotEmpty(paramJson)) {
            tablesToCopy = paramJson.getObject("tables", new TypeReference<List<String>>() {});
            dateList = paramJson.getObject("dates", new TypeReference<List<String>>() {});
        }
        
        if (dateList == null || dateList.isEmpty()) {
            // 默认日期列表
            dateList = ListUtil.toList( "2024-04-01", "2024-05-01", "2024-06-01", "2024-07-01",
                    "2024-08-01", "2024-09-01", "2024-10-01", "2024-11-01", "2024-12-01", "2025-04-01");
        }
        
        // 如果没有指定表，则复制所有表
        boolean copyAll = tablesToCopy == null || tablesToCopy.isEmpty();
        
        for (String start : dateList) {
            DateTime copyTime = DateUtil.parse(start, "yyyy-MM-dd");
            DateTime endTime = DateUtil.endOfMonth(copyTime);
            
            if (copyAll || tablesToCopy.contains("ckc")) {
                // 删除该月除第一天外的所有数据
                deleteCkcMonthData(copyTime);
                // 复制第一天的数据
                copyCkcData(copyTime, endTime);
            }
            
            if (copyAll || tablesToCopy.contains("ds")) {
                deleteDsMonthData(copyTime);
                copyDsData(copyTime, endTime);
            }
            
            if (copyAll || tablesToCopy.contains("sx")) {
                deleteSxMonthData(copyTime);
                copySxData(copyTime, endTime);
            }
            
            if (copyAll || tablesToCopy.contains("jw")) {
                deleteJwMonthData(copyTime);
                copyJwData(copyTime, endTime);
            }
            
            if (copyAll || tablesToCopy.contains("xjs")) {
                deleteXjsMonthData(copyTime);
                copyXjsData(copyTime, endTime);
            }
            
            if (copyAll || tablesToCopy.contains("jh")) {
                deleteJhMonthData(copyTime);
                copyJhData(copyTime, endTime);
            }
            
            if (copyAll || tablesToCopy.contains("jg")) {
                deleteJgMonthData(copyTime);
                copyJgData(copyTime, endTime);
            }
            
            if (copyAll || tablesToCopy.contains("qtfy")) {
                deleteQtfyMonthData(copyTime);
                copyQtfyData(copyTime, endTime);
            }

            if (copyAll || tablesToCopy.contains("mnlrday")) {
                deleteMnlrdayDayData(copyTime);
                copyMnlrdayData(copyTime, endTime);
            }

            if (copyAll || tablesToCopy.contains("frdw")) {
                deleteFrdwMonthData(copyTime);
                copyFrdwData(copyTime, endTime);
            }
        }

        log.info("复制表结束");
    }
    
    // 删除当月除第一天外的ckc数据
    private void deleteCkcMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        ckcDayService.lambdaQuery()
                .ge(CwCkcDay::getRecordTime, startOfSecondDay)
                .le(CwCkcDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> ckcDayService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的ckc数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制ckc第一天数据到月底
    private void copyCkcData(DateTime copyTime, DateTime endTime) {
        List<CwCkcDay> days = ckcDayService.lambdaQuery().eq(CwCkcDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwCkcDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            ckcDayService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("ckc复制表成功");
    }
    
    // 删除当月除第一天外的ds数据
    private void deleteDsMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        dsDayService.lambdaQuery()
                .ge(CwDsDay::getRecordTime, startOfSecondDay)
                .le(CwDsDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> dsDayService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的ds数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制ds第一天数据到月底
    private void copyDsData(DateTime copyTime, DateTime endTime) {
        List<CwDsDay> days = dsDayService.lambdaQuery().eq(CwDsDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwDsDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            dsDayService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("ds复制表成功");
    }
    
    // 删除当月除第一天外的sx数据
    private void deleteSxMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        sxDayService.lambdaQuery()
                .ge(CwSxDay::getRecordTime, startOfSecondDay)
                .le(CwSxDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> sxDayService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的sx数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制sx第一天数据到月底
    private void copySxData(DateTime copyTime, DateTime endTime) {
        List<CwSxDay> days = sxDayService.lambdaQuery().eq(CwSxDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwSxDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            sxDayService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("sx复制表成功");
    }
    
    // 删除当月除第一天外的jw数据
    private void deleteJwMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        jwDayService.lambdaQuery()
                .ge(CwJwDay::getRecordTime, startOfSecondDay)
                .le(CwJwDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> jwDayService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的jw数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制jw第一天数据到月底
    private void copyJwData(DateTime copyTime, DateTime endTime) {
        List<CwJwDay> days = jwDayService.lambdaQuery().eq(CwJwDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwJwDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            jwDayService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("jw复制表成功");
    }
    
    // 删除当月除第一天外的xjs数据
    private void deleteXjsMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        xjsDayService.lambdaQuery()
                .ge(CwXjsDay::getRecordTime, startOfSecondDay)
                .le(CwXjsDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> xjsDayService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的xjs数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制xjs第一天数据到月底
    private void copyXjsData(DateTime copyTime, DateTime endTime) {
        List<CwXjsDay> days = xjsDayService.lambdaQuery().eq(CwXjsDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwXjsDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            xjsDayService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("xjs复制表成功");
    }
    
    // 删除当月除第一天外的jh数据
    private void deleteJhMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        jhDayService.lambdaQuery()
                .ge(CwJhDay::getRecordTime, startOfSecondDay)
                .le(CwJhDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> jhDayService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的jh数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制jh第一天数据到月底
    private void copyJhData(DateTime copyTime, DateTime endTime) {
        List<CwJhDay> days = jhDayService.lambdaQuery().eq(CwJhDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwJhDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            jhDayService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("jh复制表成功");
    }
    
    // 删除当月除第一天外的jg数据
    private void deleteJgMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        jgDayService.lambdaQuery()
                .ge(CwJgDay::getRecordTime, startOfSecondDay)
                .le(CwJgDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> jgDayService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的jg数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制jg第一天数据到月底
    private void copyJgData(DateTime copyTime, DateTime endTime) {
        List<CwJgDay> days = jgDayService.lambdaQuery().eq(CwJgDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwJgDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            jgDayService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("jg复制表成功");
    }
    
    // 删除当月除第一天外的qtfy数据
    private void deleteQtfyMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        qtfyService.lambdaQuery()
                .ge(CwQtfy::getRecordTime, startOfSecondDay)
                .le(CwQtfy::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> qtfyService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的qtfy数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制qtfy第一天数据到月底
    private void copyQtfyData(DateTime copyTime, DateTime endTime) {
        List<CwQtfy> days = qtfyService.lambdaQuery().eq(CwQtfy::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwQtfy day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            qtfyService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("其他费用复制表成功");
    }

    // 删除当月除第一天外的mnlrday数据
    private void deleteMnlrdayDayData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);   
        mnlrDayService.lambdaQuery()
                .ge(CwMnlrDay::getRecordTime, startOfSecondDay)
                .le(CwMnlrDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> mnlrDayService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的mnlrday数据", DateUtil.format(firstDay, "yyyy-MM"));
    }   

    // 复制mnlrday第一天数据到月底
    private void copyMnlrdayData(DateTime copyTime, DateTime endTime) {
        List<CwMnlrDay> days = mnlrDayService.lambdaQuery().eq(CwMnlrDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {
            for (CwMnlrDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            mnlrDayService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("模拟利润复制表成功"); 
    }

    // 删除当月除第一天外的frdw数据
    private void deleteFrdwMonthData(DateTime firstDay) {
        DateTime startOfSecondDay = DateUtil.beginOfDay(DateUtil.offsetDay(firstDay, 1));
        DateTime endOfMonth = DateUtil.endOfMonth(firstDay);
        frdwService.lambdaQuery()
                .ge(CwFrdwDay::getRecordTime, startOfSecondDay)
                .le(CwFrdwDay::getRecordTime, endOfMonth)
                .list()
                .forEach(item -> frdwService.removeById(item.getId()));
        log.info("已删除{}月除第一天外的frdw数据", DateUtil.format(firstDay, "yyyy-MM"));
    }
    
    // 复制frdw第一天数据到月底
    private void copyFrdwData(DateTime copyTime, DateTime endTime) {
        List<CwFrdwDay> days = frdwService.lambdaQuery().eq(CwFrdwDay::getRecordTime, copyTime).list();
        int i = 1;
        DateTime idx = DateUtil.offsetDay(copyTime, i);
        while (idx.isBefore(endTime) || idx.equals(endTime)) {  
            for (CwFrdwDay day : days) {
                day.setRecordTime(idx);
                day.setCreateTime(null);
                day.setId(null);
            }
            frdwService.saveBatch(days);
            i++;
            idx = DateUtil.offsetDay(copyTime, i);
        }
        log.info("法人单位复制表成功");
    }
    
}
